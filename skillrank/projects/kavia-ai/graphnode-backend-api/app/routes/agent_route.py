from fastapi.responses import StreamingResponse , JSONResponse

from app.connection.establish_db_connection import get_node_db, get_vector_db , get_mongo_db
from app.connection.llm_init import get_llm_interface

from fastapi import APIRouter, Depends, Body , Query, Request, responses, HTTPException
from app.models.node_model import ConfigureNodeRequest

import asyncio
import json
import time
import uuid

import traceback
import logging
import pkgutil
import importlib
import inspect
from app.utils.node_utils import get_node_type
# Discover all classes in the 'app.discussions.types' package
# Import the package correctly. Assuming 'discussions.types' is within a parent package 'discussions'.

from app.agents.ai_architect import ArchitectureAgent
from app.agents.ai_projectmanager import ProjectManagerAgent
from app.agents.ai_softwaredesigner import SoftwareDesignerAgent
from app.agents.ai_epicmanager import EpicManagerAgent
from app.agents.ai_userstorymanager import UserStoryManagerAgent
from app.agents.supervisor import Supervisor

package = importlib.import_module("app.discussions.types")

for loader, module_name, is_pkg in pkgutil.iter_modules(package.__path__, package.__name__ + '.'):
    # Ensure the imported modules are correctly referenced with their full package names
    importlib.import_module(module_name)
# Discover and register all classes in the 'app.discussions.types' package


tasks = {}

_SHOW_NAME = "agent"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

@router.post('/{node_type}/{user_level}')
async def configure_node(node_type: str, user_level:int,request: ConfigureNodeRequest, mongo_db = Depends(get_mongo_db)):
    print("Received configuration request for node type:", node_type)  
    
    node_id = request.node_id
    project_id = node_id
    levels = request.levels
    task_id = str(uuid.uuid4())
    logging_context = {
        'mongo_handler': mongo_db,
        'task_id': task_id,
    }
    semaphore = asyncio.Semaphore(1)

    project_manager = ProjectManagerAgent(
        name="project_manager",
        node_id=node_id,  # Replace with actual node_id
        node_type='Project',  # Replace with actual node_type
        root_node_type='Project',  # Replace with actual root_node_type
        discussion_type='autoconfig',
        levels = 2,
        semaphore = semaphore,
        logging_context =logging_context  # Replace with actual configuration_type
    )
    
    DesignerAgent = SoftwareDesignerAgent(
        name="DesignerAgent",
        node_id=node_id,  # Replace with actual node_id
        node_type='Architecture',  # Replace with actual node_type
        root_node_type='Product',  # Replace with actual root_node_type
        discussion_type='autoconfig',  # Replace with actual configuration_type
        levels = 2,
        semaphore = semaphore,
        logging_context =logging_context   # Replace with actual configuration_type
    )
    architecture_agent = ArchitectureAgent(
        name="architecture_agent",
        node_id=node_id,
        node_type='Architecture',
        root_node_type='Product',
        discussion_type='autoconfig',
        logging_context=logging_context,
        levels=3,
        semaphore=semaphore
    )

    epic_agent = EpicManagerAgent(
        name="epic_agent",
        node_id=node_id,
        node_type='Epic',
        root_node_type='Product',
        discussion_type='autoconfig',
        logging_context=logging_context,
        levels=3,
        semaphore=semaphore
    )
    userstory_agent = UserStoryManagerAgent(
        name="userstory_agent",
        node_id=node_id,
        node_type='UserStory',
        root_node_type='Product',
        discussion_type='autoconfig',
        logging_context=logging_context,
        levels=3,
        semaphore=semaphore
    )
    
    
    supervisor = Supervisor(name="Supervisor", agents={"ArchitectureAgent": architecture_agent,"ProjectAgent": project_manager,"DesignerAgent": DesignerAgent,"epic_agent": epic_agent,"userstory_agent": userstory_agent},user_level=user_level, architecture_level=3, start_config="project_autoconfig", config_types=["project_autoconfig"])
    await supervisor.start(node_id, node_type, project_id)


    project_manager.supervisor = supervisor  
    architecture_agent.supervisor = supervisor
    DesignerAgent.supervisor = supervisor
    epic_agent.supervisor = supervisor
    userstory_agent.supervisor = supervisor

    asyncio.create_task(supervisor.run())
    asyncio.create_task(project_manager.run())
    asyncio.create_task(architecture_agent.run())
    asyncio.create_task(DesignerAgent.run())
    asyncio.create_task(epic_agent.run())
    asyncio.create_task(userstory_agent.run())


    return {'task_id': task_id}

    