/usr/local/lib/python3.11/site-packages/pymilvus/client/__init__.py:6: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
/usr/local/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:app.connection.mongo_client:MongoDB connection successful
INFO:websocket:Websocket connected
/usr/local/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
[2025-08-11 07:48:16] Is codegen :  False
[2025-08-11 07:48:16] 2025-08-11T07:48:08.415032+00:00 🔄 Starting Google credentials setup in daemon thread...
[2025-08-11 07:48:16] 2025-08-11T07:48:08.415106+00:00 🔄 Fetching Google credentials from secrets...
[2025-08-11 07:48:16] 2025-08-11T07:48:08.420318+00:00 Google credentials setup started in daemon thread 2025-08-11T07:48:08.420336+00:00
[2025-08-11 07:48:16] 2025-08-11T07:48:08.420394+00:00 Current PYTHONPATH: /app
[2025-08-11 07:48:16] 2025-08-11T07:48:08.420419+00:00 Current sys.path: ['/app/app/batch_jobs', '/app', '/usr/local/lib/python311.zip', '/usr/local/lib/python3.11', '/usr/local/lib/python3.11/lib-dynload', '/usr/local/lib/python3.11/site-packages', '/usr/local/lib/python3.11/site-packages/setuptools/_vendor', '/app']
[2025-08-11 07:48:16] 2025-08-11T07:48:09.231458+00:00 📋 Type of credentials: <class 'str'>
[2025-08-11 07:48:16] 2025-08-11T07:48:09.232023+00:00 ✅ GOOGLE_APPLICATION_CREDENTIALS set to: /app/google_credentials.json
[2025-08-11 07:48:16] 2025-08-11T07:48:09.232045+00:00 ✅ Google credentials daemon setup completed: /app/google_credentials.json
[2025-08-11 07:48:16] Celery app configured with url amqps://admin:<EMAIL>:5671
[2025-08-11 07:48:16] base_path /app/app/core/datamodel.json
[2025-08-11 07:48:16] 
[2025-08-11 07:48:16] 2025-08-11T07:48:11.543354+00:00 PHASE 1, START TIME:  2025-08-11T07:48:11.543373+00:00
[2025-08-11 07:48:16] 2025-08-11T07:48:11.544396+00:00 {'project_id': 90315, 'task_id': 'cg0afa38e3', 'llm_model': 'bedrock/converse/us.anthropic.claude-3-7-sonnet-20250219-v1:0', 'tenant_id': 'rdk7542', 'agent_name': 'CodeGeneration', 'platform': 'web', 'user_id': '14d8e428-5001-701f-dee2-9f12f5ce8c56', 'architecture_id': None}
[2025-08-11 07:48:16] 2025-08-11T07:48:11.544441+00:00 True
[2025-08-11 07:48:16] 2025-08-11T07:48:11.544459+00:00 ['screen', '-L', '-Logfile', '/tmp/kavia/workspace/logs/cg0afa38e3.log', '-dmS', 'cg0afa38e3', 'python', 'app/batch_jobs/jobs.py', '--input_args', '{"project_id": 90315, "task_id": "cg0afa38e3", "llm_model": "bedrock/converse/us.anthropic.claude-3-7-sonnet-20250219-v1:0", "tenant_id": "rdk7542", "agent_name": "CodeGeneration", "platform": "web", "user_id": "14d8e428-5001-701f-dee2-9f12f5ce8c56", "architecture_id": null}', '--stage', 'dev']
[2025-08-11 07:48:16] 2025-08-11T07:48:11.544475+00:00 Tenant_id for code generation rdk7542
[2025-08-11 07:48:16] Getting NodeDB connection for tenant: developrdk7542
[2025-08-11 07:48:16] Starting connection attempt...
[2025-08-11 07:48:16] Attempt 1 of 5
[2025-08-11 07:48:16] Connection and registration successful
[2025-08-11 07:48:16] 2025-08-11T07:48:12.002504+00:00 {'_id': 'cg0afa38e3', 'application_preivew_url': 'https://vscode-internal-23169-dev.dev01.cloud.kavia.ai', 'container_id': [90324], 'container_ids': [90324], 'context': {}, 'description': 'A simple Todo app that allows users to add, update, complete, and delete tasks. The application features an intuitive UI for managing tasks and supports basic CRUD operations.', 'encrypted_scm_id': '', 'iframe': 'https://vscode-internal-23169-dev.dev01.cloud.kavia.ai/?folder=/home/<USER>/workspace/code-generation/', 'ip': '127.0.0.1', 'job_id': 'cg0afa38e3', 'llm_model': 'bedrock/converse/us.anthropic.claude-3-7-sonnet-20250219-v1:0', 'new_repo_creation': True, 'params': '?project_id=90315&task_id=cg0afa38e3&stage=dev&llm_model=bedrock/converse/us.anthropic.claude-3-7-sonnet-20250219-v1:0&tenant_id=rdk7542&platform=web&pod_id=23169&user_id=14d8e428-5001-701f-dee2-9f12f5ce8c56', 'platform': 'web', 'pod_id': '23169', 'pod_name': '23169', 'ports': [{'3000': 'https://vscode-internal-23169-dev.dev01.cloud.kavia.ai/?folder=/home/<USER>/workspace/code-generation/'}, {'5000': 'https://vscode-internal-23169-dev.dev01.cloud.kavia.ai/?folder=/home/<USER>/workspace/code-generation/'}, {'8000': 'https://vscode-internal-23169-dev.dev01.cloud.kavia.ai/?folder=/home/<USER>/workspace/code-generation/'}], 'project_id': 90315, 'resume': False, 'session_name': 'Simple Todo Manager', 'stage': 'dev', 'start_time': '2025-08-11T07:47:42.811476+00:00', 'status': 'SUBMITTED', 'user_id': '14d8e428-5001-701f-dee2-9f12f5ce8c56'}
[2025-08-11 07:48:16] Host with scheme extracted: https://vscode-internal-23169-dev.dev01.cloud.kavia.ai
[2025-08-11 07:48:16] 2025-08-11T07:48:12.002654+00:00 Host for iframe https://vscode-internal-23169-dev.dev01.cloud.kavia.ai
[2025-08-11 07:48:16] 2025-08-11T07:48:12.002726+00:00 agent details {"llm_model":"gpt-4.1","agent_name":"CodeGeneration"}
[2025-08-11 07:48:16] 2025-08-11T07:48:12.002760+00:00 Starting code generation job
[2025-08-11 07:48:16] 2025-08-11T07:48:12.002768+00:00 Executing code generation for project 90315
[2025-08-11 07:48:16] [2025-08-11T07:48:12.002876+00:00] Starting document restore for resume: cg0afa38e3
[2025-08-11 07:48:16] [2025-08-11T07:48:12.029290+00:00] Restoring docs from MongoDB for task: cg0afa38e3
[2025-08-11 07:48:16] [2025-08-11T07:48:12.032638+00:00] No documents found in MongoDB for task cg0afa38e3, skipping kavia-docs creation
[2025-08-11 07:48:16] 2025-08-11T07:48:12.032704+00:00 ℹ️ No documents found in MongoDB, skipping kavia-docs creation
[2025-08-11 07:48:16] 2025-08-11T07:48:12.195275+00:00 container_ids [90324]
[2025-08-11 07:48:16] 2025-08-11T07:48:12.195306+00:00 {}
[2025-08-11 07:48:16] 2025-08-11T07:48:12.197776+00:00 🔒 Acquired repository lock for project 90315, container 90324
[2025-08-11 07:48:16] 2025-08-11T07:48:12.199961+00:00 Creating repository in workspace
[2025-08-11 07:48:16] 2025-08-11T07:48:16.417289+00:00 Repository meta data
[2025-08-11 07:48:16] 2025-08-11T07:48:16.417312+00:00 {'service': 'github', 'repositoryName': 'simple-todo-manager-90315-90324', 'repositoryId': '1035861302', 'cloneUrlHttp': 'https://github.com/kavia-common/simple-todo-manager-90315-90324.git', 'cloneUrlSsh': '**************:kavia-common/simple-todo-manager-90315-90324.git', 'organization': 'kavia-common', 'access_token_path': 'settings', 'repositoryStatus': 'initialized'}
[2025-08-11 07:48:16] query---> MATCH (n) WHERE ID(n) = $node_id SET n.repositories = $repositories RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties 
[2025-08-11 07:48:16] params--->> {'node_id': 90315, 'repositories': '{"90324": {"service": "github", "repositoryName": "simple-todo-manager-90315-90324", "repositoryId": "1035861302", "cloneUrlHttp": "https://github.com/kavia-common/simple-todo-manager-90315-90324.git", "cloneUrlSsh": "**************:kavia-common/simple-todo-manager-90315-90324.git", "organization": "kavia-common", "access_token_path": "settings", "repositoryStatus": "initialized"}}'}
[2025-08-11 07:48:16] 2025-08-11T07:48:16.436326+00:00 2025-08-11T07:48:16.436349+00:00 Started background clone thread for repository: simple-todo-manager-90315-90324
[2025-08-11 07:48:16] 2025-08-11T07:48:16.436366+00:00 🔓 Released repository lock for project 90315, container 90324
[2025-08-11 07:48:16] query---> MATCH (n) WHERE ID(n) = $node_id SET n.repositories = $repositories RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties 
[2025-08-11 07:48:16] params--->> {'node_id': 90315, 'repositories': '{"90324": {"service": "github", "repositoryName": "simple-todo-manager-90315-90324", "repositoryId": "1035861302", "cloneUrlHttp": "https://github.com/kavia-common/simple-todo-manager-90315-90324.git", "cloneUrlSsh": "**************:kavia-common/simple-todo-manager-90315-90324.git", "organization": "kavia-common", "access_token_path": "settings", "repositoryStatus": "initialized", "access_token": "*********************************************************************************************", "container_id": "90324", "container_name": "todo_frontend"}}'}
[2025-08-11 07:48:16] 2025-08-11T07:48:16.437274+00:00 🔒 Added PID 543 to filesystem lock for simple-todo-manager-90315-90324
[2025-08-11 07:48:16] 2025-08-11T07:48:16.437532+00:00 🔄 Starting background clone for repository: simple-todo-manager-90315-90324
[2025-08-11 07:48:16] Successfully saved manifest to /home/<USER>/workspace/code-generation/.project_manifest.yaml
[2025-08-11 07:48:16] overview_data --------------------------------------------------------------------------> {'project_name': 'simple_todo_app', 'description': 'A simple Todo app that allows users to add, update, complete, and delete tasks. The application features an intuitive UI for managing tasks and supports basic CRUD operations.', 'env': {}}
[2025-08-11 07:48:16] 2025-08-11T07:48:16.471426+00:00 [] [Container({'container_name': 'todo_frontend', 'description': 'User interface for managing todo tasks.', 'interfaces': 'UI accessible through a web browser.', 'container_type': <ContainerType.FRONTEND: 'frontend'>, 'dependent_containers': ['todo_database'], 'workspace': 'simple-todo-manager-90315-90324', 'container_root': 'simple-todo-manager-90315-90324/todo_frontend', 'port': 3000, 'framework': 'react', 'type': '', 'buildCommand': '', 'startCommand': '', 'installCommand': '', 'lintCommand': '', 'generateOpenapiCommand': '', 'container_details': {'features': ['add tasks', 'view task list', 'edit tasks', 'mark tasks as completed', 'delete tasks'], 'colors': {'primary': '#1976d2', 'secondary': '#388e3c', 'accent': '#e64a19'}, 'theme': 'light', 'layout_description': 'Simple centered layout with an input form at the top and a vertical list of todos below.', 'style': 'minimalistic'}, 'lintConfig': '', 'routes': [], 'apiSpec': '', 'auth': None, 'schema': '', 'migrations': '', 'seed': '', 'env': {}})]
[2025-08-11 07:48:16] [2025-08-11T07:48:16.472283+00:00] Starting document restore for resume: cg0afa38e3
[2025-08-11 07:48:16] [2025-08-11T07:48:16.472310+00:00] Restoring docs from MongoDB for task: cg0afa38e3
[2025-08-11 07:48:16] [2025-08-11T07:48:16.476760+00:00] No documents found in MongoDB for task cg0afa38e3, skipping kavia-docs creation
[2025-08-11 07:48:16] 2025-08-11T07:48:16.476833+00:00 ℹ️ No documents found in MongoDB, skipping kavia-docs creation
[2025-08-11 07:48:18] 2025-08-11T07:48:16.476848+00:00 Project Details:  {'Type': 'project', 'TempData': '{"overview": {"project_name": "simple_todo_app", "description": "A simple Todo app that allows users to add, update, complete, and delete tasks. The application features an intuitive UI for managing tasks and supports basic CRUD operations.", "env": {}, "third_party_services": []}, "containers": [{"container_name": "todo_database", "description": "Stores todo tasks and related data for the app. Handles all data persistence.", "interfaces": "SQL interface for frontend queries.", "container_type": "database", "dependent_containers": [], "workspace": "todo_database_workspace", "ports": 5001, "framework": "SQLite", "type": "", "buildCommand": "", "startCommand": "", "lintCommand": "", "container_details": "Local file-based relational database suitable for simple applications.", "lintConfig": "", "routes": [], "apiSpec": "", "auth": null, "schema": "", "migrations": "", "seed": ""}, {"container_name": "todo_frontend", "description": "User interface for managing todo tasks.", "interfaces": "UI accessible through a web browser.", "container_type": "frontend", "dependent_containers": ["todo_database"], "workspace": "todo_frontend_workspace", "ports": 3000, "framework": "React JS", "type": "", "buildCommand": "", "startCommand": "", "lintCommand": "", "container_details": {"features": ["add tasks", "view task list", "edit tasks", "mark tasks as completed", "delete tasks"], "colors": {"primary": "#1976d2", "secondary": "#388e3c", "accent": "#e64a19"}, "theme": "light", "layout_description": "Simple centered layout with an input form at the top and a vertical list of todos below.", "style": "minimalistic"}, "lintConfig": "", "routes": [], "apiSpec": "", "auth": null, "schema": "", "migrations": "", "seed": ""}]}', 'architecturePattern': '', 'manifest': "overview:\n  project_name: simple_todo_app\n  description: A simple Todo app that allows users to add, update, complete, and delete\n    tasks. The application features an intuitive UI for managing tasks and supports\n    basic CRUD operations.\n  third_party_services: []\n  env: {}\ncontainers:\n- container_name: todo_database\n  description: Stores todo tasks and related data for the app. Handles all data persistence.\n  interfaces: SQL interface for frontend queries.\n  container_type: database\n  dependent_containers: []\n  workspace: todo_database_workspace\n  container_root: todo_database_workspace/todo_database\n  port: ''\n  framework: SQLite\n  type: ''\n  buildCommand: ''\n  startCommand: ''\n  installCommand: ''\n  lintCommand: ''\n  generateOpenapiCommand: ''\n  container_details: Local file-based relational database suitable for simple applications.\n  lintConfig: ''\n  routes: []\n  apiSpec: ''\n  auth: null\n  schema: ''\n  migrations: ''\n  seed: ''\n  env: {}\n- container_name: todo_frontend\n  description: User interface for managing todo tasks.\n  interfaces: UI accessible through a web browser.\n  container_type: frontend\n  dependent_containers:\n  - todo_database\n  workspace: todo_frontend_workspace\n  container_root: todo_frontend_workspace/todo_frontend\n  port: ''\n  framework: React JS\n  type: ''\n  buildCommand: ''\n  startCommand: ''\n  installCommand: ''\n  lintCommand: ''\n  generateOpenapiCommand: ''\n  container_details:\n    features:\n    - add tasks\n    - view task list\n    - edit tasks\n    - mark tasks as completed\n    - delete tasks\n    colors:\n      primary: '#1976d2'\n      secondary: '#388e3c'\n      accent: '#e64a19'\n      background: '#ffffff'\n    theme: light\n    layout_description: Simple centered layout with an input form at the top and a\n      vertical list of todos below.\n    style: minimalistic\n  lintConfig: ''\n  routes: []\n  apiSpec: ''\n  auth: null\n  schema: ''\n  migrations: ''\n  seed: ''\n  env: {}\n", 'frontend': '{"container": {"features": [{"name": "add tasks", "description": "add tasks", "isEnabled": true}, {"name": "view task list", "description": "view task list", "isEnabled": true}, {"name": "edit tasks", "description": "edit tasks", "isEnabled": true}, {"name": "mark tasks as completed", "description": "mark tasks as completed", "isEnabled": true}, {"name": "delete tasks", "description": "delete tasks", "isEnabled": true}], "colors": {"primary": "#1976d2", "secondary": "#388e3c", "accent": "#e64a19", "background": "#ffffff"}, "layoutDescription": "Simple centered layout with an input form at the top and a vertical list of todos below.", "framework": "React JS"}}', 'configuration_state': 'configured', 'Manifest': '{"overview": {"project_name": "simple_todo_app", "description": "A simple Todo app that allows users to add, update, complete, and delete tasks. The application features an intuitive UI for managing tasks and supports basic CRUD operations.", "version": "1.0.0", "env": {}, "third_party_services": []}, "containers": [{"container_name": "todo_frontend", "description": "User interface for managing todo tasks.", "interfaces": "UI accessible through a web browser.", "container_type": "frontend", "dependent_containers": ["todo_database"], "workspace": "todo_frontend_workspace", "ports": 3000, "framework": "react", "type": "", "buildCommand": "", "startCommand": "", "lintCommand": "", "container_details": {"features": ["add tasks", "view task list", "edit tasks", "mark tasks as completed", "delete tasks"], "colors": {"primary": "#1976d2", "secondary": "#388e3c", "accent": "#e64a19"}, "theme": "light", "layout_description": "Simple centered layout with an input form at the top and a vertical list of todos below.", "style": "minimalistic"}, "lintConfig": "", "routes": [], "apiSpec": "", "auth": null, "schema": "", "migrations": "", "seed": ""}]}', 'Tech_Stack': '{"frontend": ["React JS"], "backend": ["None"], "database": ["None"]}', 'work_item_type': 'project', 'created_at': '2025-08-11T07:47:23.790989+00:00', 'created_by': '14d8e428-5001-701f-dee2-9f12f5ce8c56', 'Layout_Description': 'Simple centered layout with an input form at the top and a vertical list of todos below.', 'Description': 'A simple Todo app that allows users to add, update, complete, and delete tasks. The application features an intuitive UI for managing tasks and supports basic CRUD operations.', 'is_active': True, 'frontend_framework': 'React JS', 'status': 'active', 'Colors': '{"primary": "#1976d2", "secondary": "#388e3c", "accent": "#e64a19"}', 'Title': 'Simple Todo Manager', 'Features': '[{"id": "1", "name": "add tasks", "description": "add tasks", "isEnabled": true}, {"id": "2", "name": "view task list", "description": "view task list", "isEnabled": true}, {"id": "3", "name": "edit tasks", "description": "edit tasks", "isEnabled": true}, {"id": "4", "name": "mark tasks as completed", "description": "mark tasks as completed", "isEnabled": true}, {"id": "5", "name": "delete tasks", "description": "delete tasks", "isEnabled": true}]', 'Init_project_info': '{"id": "90315", "name": "simple_todo_app", "description": "A simple Todo app that allows users to add, update, complete, and delete tasks. The application features an intuitive UI for managing tasks and supports basic CRUD operations.", "features": [{"id": "1", "name": "add tasks", "description": "add tasks", "isEnabled": true}, {"id": "2", "name": "view task list", "description": "view task list", "isEnabled": true}, {"id": "3", "name": "edit tasks", "description": "edit tasks", "isEnabled": true}, {"id": "4", "name": "mark tasks as completed", "description": "mark tasks as completed", "isEnabled": true}, {"id": "5", "name": "delete tasks", "description": "delete tasks", "isEnabled": true}], "techStack": {"frontend": ["React JS"], "backend": ["None"], "database": ["None"]}, "colors": {"primary": "#1976d2", "secondary": "#388e3c", "accent": "#e64a19"}, "theme": "light", "estimatedTime": "1-2 weeks", "complexity": "simple", "layoutDescription": "Simple centered layout with an input form at the top and a vertical list of todos below.", "architecturePattern": null}', 'Theme': 'light', 'Complexity': 'simple', 'overview': '{"project_name": "simple_todo_app", "description": "A simple Todo app that allows users to add, update, complete, and delete tasks. The application features an intuitive UI for managing tasks and supports basic CRUD operations."}', 'Estimated_Time': '1-2 weeks', 'database_framework': 'none', 'current_repositories': [{'service': 'github', 'repositoryName': 'simple-todo-manager-90315-90324', 'repositoryId': '1035861302', 'cloneUrlHttp': 'https://github.com/kavia-common/simple-todo-manager-90315-90324.git', 'cloneUrlSsh': '**************:kavia-common/simple-todo-manager-90315-90324.git', 'organization': 'kavia-common', 'access_token_path': 'settings', 'repositoryStatus': 'initialized', 'access_token': '*********************************************************************************************', 'container_id': '90324', 'container_name': 'todo_frontend'}]}
[2025-08-11 07:48:18] 2025-08-11T07:48:16.476960+00:00 WorkItem Detais:  {'project_type': 'multi_container', 'project_name': 'simple_todo_app', 'description': 'A simple Todo app that allows users to add, update, complete, and delete tasks. The application features an intuitive UI for managing tasks and supports basic CRUD operations.', 'env': {}, 'containers': [{'container_name': 'todo_frontend', 'platform': 'web', 'framework': 'react', 'description': 'User interface for managing todo tasks.', 'interfaces': 'UI accessible through a web browser.', 'workspace': 'simple-todo-manager-90315-90324', 'container_root': 'simple-todo-manager-90315-90324/todo_frontend', 'dependencies': ['todo_database'], 'container_type': 'frontend', 'container_details': {'features': ['add tasks', 'view task list', 'edit tasks', 'mark tasks as completed', 'delete tasks'], 'colors': {'primary': '#1976d2', 'secondary': '#388e3c', 'accent': '#e64a19'}, 'theme': 'light', 'layout_description': 'Simple centered layout with an input form at the top and a vertical list of todos below.', 'style': 'minimalistic'}}], 'overview': {'project_name': 'simple_todo_app', 'description': 'A simple Todo app that allows users to add, update, complete, and delete tasks. The application features an intuitive UI for managing tasks and supports basic CRUD operations.', 'env': {}, 'frontend_framework': 'react'}, '3rd_party_services': [], 'figma_components': '', 'manifest_path': '/home/<USER>/workspace/code-generation/.project_manifest.yaml'}
[2025-08-11 07:48:18] 2025-08-11T07:48:16.477082+00:00 USER ID 14d8e428-5001-701f-dee2-9f12f5ce8c56  Tenant ID rdk7542
[2025-08-11 07:48:18] 2025-08-11T07:48:16.477130+00:00 Platform is web and selected framework is : None
[2025-08-11 07:48:18]  ++ initialising new connection ++
[2025-08-11 07:48:18] 2025-08-11T07:48:16.875895+00:00 DEBUG - CodeGeneration current_repositories: [{'service': 'github', 'repositoryName': 'simple-todo-manager-90315-90324', 'repositoryId': '1035861302', 'cloneUrlHttp': 'https://github.com/kavia-common/simple-todo-manager-90315-90324.git', 'cloneUrlSsh': '**************:kavia-common/simple-todo-manager-90315-90324.git', 'organization': 'kavia-common', 'access_token_path': 'settings', 'repositoryStatus': 'initialized', 'access_token': '*********************************************************************************************', 'container_id': '90324', 'container_name': 'todo_frontend'}]
[2025-08-11 07:48:18] 2025-08-11T07:48:16.875936+00:00 DEBUG - Selected repository_metadata: {'service': 'github', 'repositoryName': 'simple-todo-manager-90315-90324', 'repositoryId': '1035861302', 'cloneUrlHttp': 'https://github.com/kavia-common/simple-todo-manager-90315-90324.git', 'cloneUrlSsh': '**************:kavia-common/simple-todo-manager-90315-90324.git', 'organization': 'kavia-common', 'access_token_path': 'settings', 'repositoryStatus': 'initialized', 'access_token': '*********************************************************************************************', 'container_id': '90324', 'container_name': 'todo_frontend'}
[2025-08-11 07:48:18] Sending message:  {'content': 'Cloning repository: simple-todo-manager-90315-90324 (Container ID: 90324)...', 'sender': 'AI', 'timestamp': '2025-08-11T07:48:16.875964+00:00', 'user_id': 'admin', 'id': '65b5fc9c-8ac5-41b5-bfb7-96f1e64ade18', 'status': 'RESOLVED', 'msg_type': 'SYSTEM'}
[2025-08-11 07:48:18] Sending message:  {'content': 'Successfully initialized workspace for simple-todo-manager-90315-90324 at: /home/<USER>/workspace/code-generation/simple-todo-manager-90315-90324', 'sender': 'AI', 'timestamp': '2025-08-11T07:48:16.891827+00:00', 'user_id': 'admin', 'id': 'db7d0ace-d63d-4b70-a2b7-94865326eb3b', 'status': 'RESOLVED', 'msg_type': 'SYSTEM'}
[2025-08-11 07:48:18] Sending message:  {'content': 'Setting Base path /home/<USER>/workspace/code-generation...', 'sender': 'AI', 'timestamp': '2025-08-11T07:48:16.901902+00:00', 'user_id': 'admin', 'id': '5f25a833-8587-492d-94f3-0eaf688f2d41', 'status': 'RESOLVED', 'msg_type': 'SYSTEM'}
[2025-08-11 07:48:18] Event 'llm.timeoutevent' reported successfully
[2025-08-11 07:48:18] Event 'llm.timeout.count' reported successfully
[2025-08-11 07:48:18] 2025-08-11T07:48:17.572509+00:00 Added repository to Git safe directories: /home/<USER>/workspace/code-generation/simple-todo-manager-90315-90324
[2025-08-11 07:48:18] 2025-08-11T07:48:17.580006+00:00 Set core.filemode to false for repository: /home/<USER>/workspace/code-generation/simple-todo-manager-90315-90324
[2025-08-11 07:48:18] 2025-08-11T07:48:17.606863+00:00 ### SESSION MANAGEMENT ###
[2025-08-11 07:48:18] 2025-08-11T07:48:17.606894+00:00 ### Creating session branches
[2025-08-11 07:48:18] Sending message:  {'content': 'Workspace environment is ready to use', 'sender': 'AI', 'timestamp': '2025-08-11T07:48:17.872193+00:00', 'user_id': 'admin', 'id': '50def9de-526a-445d-b5f1-fbda3c8cf22e', 'status': 'RESOLVED', 'msg_type': 'SYSTEM'}
[2025-08-11 07:48:18] 2025-08-11T07:48:17.875871+00:00 DEBUG - Final repository_name: simple-todo-manager-90315-90324
[2025-08-11 07:48:18] 2025-08-11T07:48:17.875897+00:00 DEBUG - Available keys in repository_metadata: ['service', 'repositoryName', 'repositoryId', 'cloneUrlHttp', 'cloneUrlSsh', 'organization', 'access_token_path', 'repositoryStatus', 'access_token', 'container_id', 'container_name']
[2025-08-11 07:48:18] 2025-08-11T07:48:17.876566+00:00 TaskExecutionAgent does not have request_context attribute, skipping design knowledge setup
[2025-08-11 07:48:18] Ensuring queue directory exists: /home/<USER>/workspace/.queue
[2025-08-11 07:48:18] Starting connection attempt...
[2025-08-11 07:48:18] Already connected, returning
[2025-08-11 07:48:18] Initializing filewatch for /home/<USER>/workspace/code-generation/.filesystem.lock
[2025-08-11 07:48:18] base_path: /home/<USER>/workspace/code-generation
[2025-08-11 07:48:18] ************************************syncup the temp-attacuments folder******************
[2025-08-11 07:48:18] kavia-attachments-dev-rdk7542
[2025-08-11 07:48:18] kavia-attachments-dev-rdk7542
[2025-08-11 07:48:18] Ensuring queue directory exists: /home/<USER>/workspace/.queue
[2025-08-11 07:48:18] 2025-08-11T07:48:17.888693+00:00 Inside the set Deployment controller
[2025-08-11 07:48:18] 2025-08-11T07:48:17.965275+00:00 Fetched latest remote branches
[2025-08-11 07:48:18] 2025-08-11T07:48:17.973668+00:00 Found remote branches: ['kavia-main', 'main']
[2025-08-11 07:48:18] 2025-08-11T07:48:17.973736+00:00 kavia-main branch does not exist locally, creating it...
[2025-08-11 07:48:18] 2025-08-11T07:48:17.973746+00:00 kavia-main exists remotely, creating tracking branch...
[2025-08-11 07:48:18] 2025-08-11T07:48:17.993246+00:00 Stashed untracked files
[2025-08-11 07:48:18] 2025-08-11T07:48:18.021469+00:00 Error tracking remote kavia-main: Cmd('git') failed due to: exit code(128)
[2025-08-11 07:48:18]   cmdline: git checkout -b kavia-main --track origin/kavia-main
[2025-08-11 07:48:18]   stderr: 'fatal: A branch named 'kavia-main' already exists.'
[2025-08-11 07:48:18] 2025-08-11T07:48:18.021712+00:00 Falling back to creating from current branch: kavia-main
[2025-08-11 07:48:18] 2025-08-11T07:48:18.035474+00:00 Error: Could not create kavia-main branch: Cmd('git') failed due to: exit code(128)
[2025-08-11 07:48:18]   cmdline: git checkout -b kavia-main
[2025-08-11 07:48:18]   stderr: 'fatal: A branch named 'kavia-main' already exists.'
[2025-08-11 07:48:18] 2025-08-11T07:48:18.063137+00:00 Created and switched to new branch: cga-cg0afa38e3 from kavia-main
[2025-08-11 07:48:18] 2025-08-11T07:48:18.070177+00:00 Inside the set_container_deployment_controller 
[2025-08-11 07:48:18] 2025-08-11T07:48:18.070326+00:00 Inside the set preview controller
[2025-08-11 07:48:18] self.projectId 123
[2025-08-11 07:48:18] Starting connection attempt...
[2025-08-11 07:48:18] Already connected, returning
[2025-08-11 07:48:18] 2025-08-11T07:48:18.083964+00:00 previous_context {'_id': 'cg0afa38e3'}
[2025-08-11 07:48:18] 2025-08-11T07:48:18.083995+00:00 previous_context {}
WARNING:browsergym.core.env:Overriding the task's viewport parameter ({'width': 1280, 'height': 720} => {'width': 1200, 'height': 1600}). This might change the task's behaviour and difficulty.
INFO:botocore.credentials:Found credentials in environment variables.
INFO:app.classes.S3Handler:Successfully initialized S3Handler with buckets: kavia-attachments-dev-rdk7542 and kavia-profile-pictures-dev
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/code_generation_core_agent/agents/tools/web_browser.py", line 131, in run
    obs, info = globalBrowserEnvironment.reset()
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/browsergym/core/env.py", line 232, in reset
    self.browser = pw.chromium.launch(
                   ^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/playwright/sync_api/_generated.py", line 13991, in launch
    self._sync(
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_sync_base.py", line 115, in _sync
    return task.result()
           ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/asyncio/futures.py", line 203, in result
    raise self._exception.with_traceback(self._exception_tb)
  File "/usr/local/lib/python3.11/asyncio/tasks.py", line 277, in __step
    result = coro.send(None)
             ^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 94, in launch
    Browser, from_channel(await self._channel.send("launch", params))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 59, in send
    return await self._connection.wrap_api_call(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 514, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
playwright._impl._errors.Error: BrowserType.launch: 
╔══════════════════════════════════════════════════════╗
║ Host system is missing dependencies to run browsers. ║
║ Please install them with the following command:      ║
║                                                      ║
║     playwright install-deps                          ║
║                                                      ║
║ Alternatively, use apt:                              ║
║     apt-get install libatk1.0-0\                     ║
║         libatk-bridge2.0-0\                          ║
║         libcups2\                                    ║
║         libatspi2.0-0\                               ║
║         libxdamage1\                                 ║
║         libgbm1\                                     ║
║         libxkbcommon0\                               ║
║         libpango-1.0-0\                              ║
║         libcairo2\                                   ║
║         libasound2                                   ║
║                                                      ║
║ <3 Playwright Team                                   ║
╚══════════════════════════════════════════════════════╝
[92m07:48:27 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m07:48:28 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m07:48:28 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m07:48:28 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m07:48:28 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m07:48:29 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m07:48:29 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m07:48:29 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m07:48:30 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m07:48:30 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m07:48:30 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[92m07:48:30 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[2025-08-11 07:48:30] 2025-08-11T07:48:18.084006+00:00 Work_ITem_details: Type: <class 'dict'>, details: {'project_type': 'multi_container', 'project_name': 'simple_todo_app', 'description': 'A simple Todo app that allows users to add, update, complete, and delete tasks. The application features an intuitive UI for managing tasks and supports basic CRUD operations.', 'env': {}, 'containers': [{'container_name': 'todo_frontend', 'platform': 'web', 'framework': 'react', 'description': 'User interface for managing todo tasks.', 'interfaces': 'UI accessible through a web browser.', 'workspace': 'simple-todo-manager-90315-90324', 'container_root': 'simple-todo-manager-90315-90324/todo_frontend', 'dependencies': ['todo_database'], 'container_type': 'frontend', 'container_details': {'features': ['add tasks', 'view task list', 'edit tasks', 'mark tasks as completed', 'delete tasks'], 'colors': {'primary': '#1976d2', 'secondary': '#388e3c', 'accent': '#e64a19'}, 'theme': 'light', 'layout_description': 'Simple centered layout with an input form at the top and a vertical list of todos below.', 'style': 'minimalistic'}}], 'overview': {'project_name': 'simple_todo_app', 'description': 'A simple Todo app that allows users to add, update, complete, and delete tasks. The application features an intuitive UI for managing tasks and supports basic CRUD operations.', 'env': {}, 'frontend_framework': 'react'}, '3rd_party_services': [], 'figma_components': '', 'manifest_path': '/home/<USER>/workspace/code-generation/.project_manifest.yaml'} 
[2025-08-11 07:48:30] 2025-08-11T07:48:18.089136+00:00  No Supabase credentials found for project None
[2025-08-11 07:48:30] 2025-08-11T07:48:18.089207+00:00 AGENT :  CodeGeneration
[2025-08-11 07:48:30] 2025-08-11T07:48:18.089224+00:00 PHASE TASK EXECUTION, START TIME:  2025-08-11T07:48:18.089231+00:00
[2025-08-11 07:48:30] 2025-08-11T07:48:18.089376+00:00 💰 Getting budget for user: 14d8e428-5001-701f-dee2-9f12f5ce8c56, tenant: rdk7542
[2025-08-11 07:48:30] 2025-08-11T07:48:18.089394+00:00 🏢 Is B2C tenant: False
[2025-08-11 07:48:30] 2025-08-11T07:48:18.089406+00:00 ⚠️ Not a B2C tenant, returning unlimited budget
[2025-08-11 07:48:30] 2025-08-11T07:48:18.089447+00:00 ===============================BUDGET=========================== inf
[2025-08-11 07:48:30] Starting progress callback in thread _progress_callback_thread_daemon_139841679021888
[2025-08-11 07:48:30] 2025-08-11T07:48:18.095886+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:18.095875+00:00
[2025-08-11 07:48:30] PROGRESSCALLBACK {'status': 'Starting task execution process for CodeGeneration...', 'total_tasks': '', 'latest_result': '', 'step': None}
[2025-08-11 07:48:30] <app.core.code_generation.Controller object at 0x7f2f20223810> <app.core.git_controller.GitController object at 0x7f2f20140b90>
[2025-08-11 07:48:30] Progress update sent to WebSocket client
[2025-08-11 07:48:30] Controller is set
[2025-08-11 07:48:30] Progress callback thread _progress_callback_thread_daemon_139841679021888 completed
[2025-08-11 07:48:30] ---- INITIALISING SupabaseTool ---- with BASE_PATH: /home/<USER>/workspace/code-generation
[2025-08-11 07:48:30] ZK (<code_generation_core_agent.agents.preview.preview_manager.PreviewManager object at 0x7f2f3cffb890>)Registering container: todo_frontend of type web at /home/<USER>/workspace/code-generation/simple-todo-manager-90315-90324
[2025-08-11 07:48:30] Successfully saved manifest to /home/<USER>/workspace/code-generation/.project_manifest.yaml
[2025-08-11 07:48:30] Universal init status callback: todo_frontend, starting, Starting universal initialization for todo_frontend, None
[2025-08-11 07:48:30] 2025-08-11T07:48:19.361740+00:00 Successfully pushed branch cga-cg0afa38e3 to origin with upstream tracking
[2025-08-11 07:48:30] 2025-08-11T07:48:19.361773+00:00 ### Getting final current branch
[2025-08-11 07:48:30] 2025-08-11T07:48:19.361990+00:00 Branch operations completed: {'kavia_main_status': 'failed_fallback_to_kavia-main', 'task_branch_status': 'created_from_kavia_main_and_pushed', 'current_branch': 'cga-cg0afa38e3', 'gitignore_update': '', 'errors': ["Track remote kavia-main failed: Cmd('git') failed due to: exit code(128)\n  cmdline: git checkout -b kavia-main --track origin/kavia-main\n  stderr: 'fatal: A branch named 'kavia-main' already exists.'", "Create kavia-main failed: Cmd('git') failed due to: exit code(128)\n  cmdline: git checkout -b kavia-main\n  stderr: 'fatal: A branch named 'kavia-main' already exists.'"]}
[2025-08-11 07:48:30] 2025-08-11T07:48:19.362059+00:00 Warning: Could not set permissions: cannot access local variable 'subprocess' where it is not associated with a value
[2025-08-11 07:48:30] 🔒 Set 777 permissions for repository: /home/<USER>/workspace/code-generation/simple-todo-manager-90315-90324
[2025-08-11 07:48:30] 2025-08-11T07:48:20.529190+00:00 ✅ Background clone completed for repository: simple-todo-manager-90315-90324 at /home/<USER>/workspace/code-generation/simple-todo-manager-90315-90324
[2025-08-11 07:48:30] 2025-08-11T07:48:20.529433+00:00 🔓 Removed PID 543 from filesystem lock for simple-todo-manager-90315-90324
[2025-08-11 07:48:30] 2025-08-11T07:48:20.529554+00:00 🗑️ Deleted empty filesystem lock file
[2025-08-11 07:48:30] Universal init status callback: todo_frontend, success, Universal initialization completed successfully for todo_frontend, None
[2025-08-11 07:48:30] Universal init status callback: overall, completed, Universal initialization completed: 1/1 containers initialized successfully, None
[2025-08-11 07:48:30] UNIVERSAL INIT COMPLETED, TRIGGERING COMMIT CHANGES
[2025-08-11 07:48:30] Starting commit changes in thread _commit_changes_thread_daemon_139841679021888
[2025-08-11 07:48:30] Starting commit changes in GitController for task cg0afa38e3
[2025-08-11 07:48:30] Processing repository: /home/<USER>/workspace/code-generation/simple-todo-manager-90315-90324
[2025-08-11 07:48:30] Commit changes triggered: <Thread(_commit_changes_thread_daemon_139841679021888, started daemon 139840432629312)>
[2025-08-11 07:48:30] ZK (<code_generation_core_agent.agents.preview.preview_manager.PreviewManager object at 0x7f2f3cffb890>)Registering container: todo_frontend of type web at /home/<USER>/workspace/code-generation/simple-todo-manager-90315-90324
[2025-08-11 07:48:30] Target branch: cga-cg0afa38e3
[2025-08-11 07:48:30] Current branch: cga-cg0afa38e3
[2025-08-11 07:48:30] Added .file_activity_report.json to .gitignore
[2025-08-11 07:48:30] Unstaged .file_activity_report.json from git
[2025-08-11 07:48:30] ✅ File exclusions configured
[2025-08-11 07:48:30] Project type ContainerType.FRONTEND
[2025-08-11 07:48:30] Checking workspace /home/<USER>/workspace/code-generation/simple-todo-manager-90315-90324
[2025-08-11 07:48:30] Successfully saved manifest to /home/<USER>/workspace/code-generation/.project_manifest.yaml
[2025-08-11 07:48:30] Git status: On branch cga-cg0afa38e3
[2025-08-11 07:48:30] Your branch is up to date with 'origin/cga-cg0afa38e3'.
[2025-08-11 07:48:30] 
[2025-08-11 07:48:30] Changes not staged for commit:
[2025-08-11 07:48:30]   (use "git add <file>..." to update what will be committed)
[2025-08-11 07:48:30]   (use "git restore <file>..." to discard changes in working directory)
[2025-08-11 07:48:30]   modified:   .gitignore
[2025-08-11 07:48:30] 
[2025-08-11 07:48:30] Untracked files:
[2025-08-11 07:48:30]   (use "git add <file>..." to include in what will be committed)
[2025-08-11 07:48:30]   .init/
[2025-08-11 07:48:30]   .knowledge/
[2025-08-11 07:48:30]   todo_frontend/
[2025-08-11 07:48:30] 
[2025-08-11 07:48:30] no changes added to commit (use "git add" and/or "git commit -a")
[2025-08-11 07:48:30] Adding all files to git staging area...
[2025-08-11 07:48:30] Git add successful: 
[2025-08-11 07:48:30] Committing with message: CheckPoint - cg0afa38e3
[2025-08-11 07:48:30] Commit result: Committed changes with message: CheckPoint - cg0afa38e3
[2025-08-11 07:48:30] ✅ Commit successful
[2025-08-11 07:48:30] Push result: Successfully pushed changes to remote.
[2025-08-11 07:48:30] Git logs retrieved: 2
[2025-08-11 07:48:30] ✅ Checkpoint is created: 97e8a8e8
[2025-08-11 07:48:30] ✅ Created new checkpoint message: Rollback 1
[2025-08-11 07:48:30] 🏁 Commit changes completed for task cg0afa38e3
[2025-08-11 07:48:30] ✅ Commit changes completed: 1 checkpoints created
[2025-08-11 07:48:30] 🏁 Commit changes thread _commit_changes_thread_daemon_139841679021888 completed
[2025-08-11 07:48:30] Starting cost update in thread Thread-16 (_worker_loop)
[2025-08-11 07:48:30] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00016839999999999997}, Total cost: 0.00016839999999999997
[2025-08-11 07:48:30] [DEBUG] Starting session tracking for task_id: cg0afa38e3
[2025-08-11 07:48:30] Starting cost update in thread Thread-15 (_worker_loop)
[2025-08-11 07:48:30] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00016839999999999997}, Total cost: 0.00016839999999999997
[2025-08-11 07:48:30] [DEBUG] Starting session tracking for task_id: cg0afa38e3
[2025-08-11 07:48:30] [DEBUG] SessionTrackerService initialized with db: develop_kaviaroot
[2025-08-11 07:48:30] [DEBUG] Updating session cost for task_id: cg0afa38e3
[2025-08-11 07:48:30] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00016839999999999997}
[2025-08-11 07:48:30] [DEBUG] Total cost: 0.00016839999999999997
[2025-08-11 07:48:30] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 07:48:30] [SUCCESS] Session cost REPLACED for task_id: cg0afa38e3
[2025-08-11 07:48:30] [SUCCESS] New total_cost: 0.00016839999999999997
[2025-08-11 07:48:30] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00016839999999999997}
[2025-08-11 07:48:30] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 07:48:30] Starting cost update in thread Thread-20 (_worker_loop)
[2025-08-11 07:48:30] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0002517}, Total cost: 0.0002517
[2025-08-11 07:48:30] [DEBUG] Starting session tracking for task_id: cg0afa38e3
[2025-08-11 07:48:30] [DEBUG] Updating session cost for task_id: cg0afa38e3
[2025-08-11 07:48:30] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0002517}
[2025-08-11 07:48:30] [DEBUG] Total cost: 0.0002517
[2025-08-11 07:48:30] Starting cost update in thread Thread-19 (_worker_loop)
[2025-08-11 07:48:30] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0003256}, Total cost: 0.0003256
[2025-08-11 07:48:30] [DEBUG] Starting session tracking for task_id: cg0afa38e3
[2025-08-11 07:48:30] [DEBUG] Updating session cost for task_id: cg0afa38e3
[2025-08-11 07:48:30] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0003256}
[2025-08-11 07:48:30] [DEBUG] Total cost: 0.0003256
[2025-08-11 07:48:30] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 07:48:30] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 7, 48, 30, 499261), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0002517}, 'total_cost': 0.0002517}
[2025-08-11 07:48:30] [SUCCESS] Session metadata updated for task_id: cg0afa38e3
[2025-08-11 07:48:30] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 07:48:30] [SUCCESS] Session cost tracking updated for task_id: cg0afa38e3
[2025-08-11 07:48:30] [SUCCESS] Costs REPLACED - Total: 0.00016839999999999997, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00016839999999999997}
[2025-08-11 07:48:30] 2025-08-11T07:48:30.500764+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:30.500756+00:00
[2025-08-11 07:48:30] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 07:48:30] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 7, 48, 30, 505202), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00016839999999999997}, 'total_cost': 0.00016839999999999997}
[2025-08-11 07:48:30] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 07:48:30] [SUCCESS] Session cost REPLACED for task_id: cg0afa38e3
[2025-08-11 07:48:30] [SUCCESS] New total_cost: 0.0002517
[2025-08-11 07:48:30] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0002517}
[2025-08-11 07:48:30] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 07:48:30] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 07:48:30] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 7, 48, 30, 509154), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0003256}, 'total_cost': 0.0003256}
[2025-08-11 07:48:30] 
[2025-08-11 07:48:30] 🔍 Starting B2B Cost Tracking
[2025-08-11 07:48:30] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:30] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:30] 💰 Total Cost: $0.000168
[2025-08-11 07:48:30] 🆔 Task ID: cg0afa38e3
[2025-08-11 07:48:30] 🏗️ Project ID: 90315
[2025-08-11 07:48:30] 🆕 Is New Task (Parameter): True
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m07:48:30 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m07:48:30 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m07:48:30 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[2025-08-11 07:48:30] ", "Title": "Simple Todo Manager", "Features": "[{\"id\": \"1\", \"name\": \"add tasks\", \"description\": \"add tasks\", \"isEnabled\": true}, {\"id\": \"2\", \"name\": \"view task list\", \"description\": \"view task list\", \"isEnabled\": true}, {\"id\": \"3\", \"name\": \"edit tasks\", \"description\": \"edit tasks\", \"isEnabled\": true}, {\"id\": \"4\", \"name\": \"mark tasks as completed\", \"description\": \"mark tasks as completed\", \"isEnabled\": true}, {\"id\": \"5\", \"name\": \"delete tasks\", \"description\": \"delete tasks\", \"isEnabled\": true}]", "Init_project_info": "{\"id\": \"90315\", \"name\": \"simple_todo_app\", \"description\": \"A simple Todo app that allows users to add, update, complete, and delete tasks. The application features an intuitive UI for managing tasks and supports basic CRUD operations.\", \"features\": [{\"id\": \"1\", \"name\": \"add tasks\", \"description\": \"add tasks\", \"isEnabled\": true}, {\"id\": \"2\", \"name\": \"view task list\", \"description\": \"view task list\", \"isEnabled\": true}, {\"id\": \"3\", \"name\": \"edit tasks\", \"description\": \"edit tasks\", \"isEnabled\": true}, {\"id\": \"4\", \"name\": \"mark tasks as comp🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00016839999999999997}
[2025-08-11 07:48:30] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 07:48:30] [SUCCESS] Session cost REPLACED for task_id: cg0afa38e3
[2025-08-11 07:48:30] [SUCCESS] New total_cost: 0.0003256
[2025-08-11 07:48:30] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0003256}
[2025-08-11 07:48:30] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 07:48:30] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 07:48:30] [SUCCESS] Session cost REPLACED for task_id: cg0afa38e3
[2025-08-11 07:48:30] [SUCCESS] New total_cost: 0.00016839999999999997
[2025-08-11 07:48:30] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00016839999999999997}
[2025-08-11 07:48:30] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 07:48:30] [SUCCESS] Session metadata updated for task_id: cg0afa38e3
[2025-08-11 07:48:30] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 07:48:30] [SUCCESS] Session cost tracking updated for task_id: cg0afa38e3
[2025-08-11 07:48:30] [SUCCESS] Costs REPLACED - Total: 0.0002517, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0002517}
[2025-08-11 07:48:30] 2025-08-11T07:48:30.521200+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:30.521194+00:00
[2025-08-11 07:48:30] [SUCCESS] Session metadata updated for task_id: cg0afa38e3
[2025-08-11 07:48:30] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 07:48:30] [SUCCESS] Session cost tracking updated for task_id: cg0afa38e3
[2025-08-11 07:48:30] [SUCCESS] Costs REPLACED - Total: 0.00016839999999999997, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00016839999999999997}
[2025-08-11 07:48:30] [SUCCESS] Session metadata updated for task_id: cg0afa38e3
[2025-08-11 07:48:30] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 07:48:30] [SUCCESS] Session cost tracking updated for task_id: cg0afa38e3
[2025-08-11 07:48:30] [SUCCESS] Costs REPLACED - Total: 0.0003256, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0003256}
[2025-08-11 07:48:30] 2025-08-11T07:48:30.530002+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:30.529995+00:00
[2025-08-11 07:48:30] 
[2025-08-11 07:48:30] 🔍 Starting B2B Cost Tracking
[2025-08-11 07:48:30] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:30] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:30] 💰 Total Cost: $0.000252
[2025-08-11 07:48:30] 🆔 Task ID: cg0afa38e3
[2025-08-11 07:48:30] 🏗️ Project ID: 90315
[2025-08-11 07:48:30] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:30] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0002517}
[2025-08-11 07:48:30] 2025-08-11T07:48:30.532969+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:30.532963+00:00
[2025-08-11 07:48:30] 
[2025-08-11 07:48:30] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 07:48:30]    Total Users: 13
[2025-08-11 07:48:30]    Organization Cost: $885.982361
[2025-08-11 07:48:30] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:30] 🆕 Creating new project entry for Project ID: 90315
[2025-08-11 07:48:30] 
[2025-08-11 07:48:30] ================================================================================
[2025-08-11 07:48:30] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 07:48:30] ================================================================================
[2025-08-11 07:48:30] 📋 Task ID: cg0afa38e3
[2025-08-11 07:48:30] 🏗️ Project ID: 90315
[2025-08-11 07:48:30] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:30] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:30] 🆕 Is New Task (Parameter): True
[2025-08-11 07:48:30] 🔍 Task Exists in Tracking: False
[2025-08-11 07:48:30] ✅ Actually New Task: True
[2025-08-11 07:48:30] --------------------------------------------------------------------------------
[2025-08-11 07:48:30] 💸 COST BREAKDOWN:
[2025-08-11 07:48:30]    Current Task Cost: $0.000168
[2025-08-11 07:48:30]    Previous Task Cost: $0.000000
[2025-08-11 07:48:30]    Operation: APPENDED
[2025-08-11 07:48:30]    Reason: New task ID - appending cost to existing user total
[2025-08-11 07:48:30] --------------------------------------------------------------------------------
[2025-08-11 07:48:30] 📊 USER COST CHANGES:
[2025-08-11 07:48:30]    Original User Cost: $402.930350
[2025-08-11 07:48:30]    New User Cost: $402.930518
[2025-08-11 07:48:30]    User Cost Delta: $+0.000168
[2025-08-11 07:48:30]    📈 APPEND LOGIC: $402.930350 + $0.000168 = $402.930518
[2025-08-11 07:48:30] --------------------------------------------------------------------------------
[2025-08-11 07:48:30] 
[2025-08-11 07:48:30] 🔍 Starting B2B Cost Tracking
[2025-08-11 07:48:30] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:30] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:30] 💰 Total Cost: $0.000326
[2025-08-11 07:48:30] 🆔 Task ID: cg0afa38e3
[2025-08-11 07:48:30] 🏗️ Project ID: 90315
[2025-08-11 07:48:30] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:30] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0003256}
[2025-08-11 07:48:30] 
[2025-08-11 07:48:30] 🔍 Starting B2B Cost Tracking
[2025-08-11 07:48:30] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:30] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:30] 💰 Total Cost: $0.000168
[2025-08-11 07:48:30] 🆔 Task ID: cg0afa38e3
[2025-08-11 07:48:30] 🏗️ Project ID: 90315
[2025-08-11 07:48:30] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:30] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00016839999999999997}
[2025-08-11 07:48:30] Starting cost update in thread Thread-18 (_worker_loop)
[2025-08-11 07:48:30] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0004565}, Total cost: 0.0004565
[2025-08-11 07:48:30] [DEBUG] Starting session tracking for task_id: cg0afa38e3
[2025-08-11 07:48:30] [DEBUG] Updating session cost for task_id: cg0afa38e3
[2025-08-11 07:48:30] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0004565}
[2025-08-11 07:48:30] [DEBUG] Total cost: 0.0004565
[2025-08-11 07:48:30] 
[2025-08-11 07:48:30] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 07:48:30]    Total Users: 13
[2025-08-11 07:48:30]    Organization Cost: $885.982361
[2025-08-11 07:48:31] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:31] 🆕 Creating new project entry for Project ID: 90315
[2025-08-11 07:48:31] 
[2025-08-11 07:48:31] ================================================================================
[2025-08-11 07:48:31] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 07:48:31] ================================================================================
[2025-08-11 07:48:31] 📋 Task ID: cg0afa38e3
[2025-08-11 07:48:31] 🏗️ Project ID: 90315
[2025-08-11 07:48:31] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:31] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:31] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:31] 🔍 Task Exists in Tracking: False
[2025-08-11 07:48:31] ✅ Actually New Task: True
[2025-08-11 07:48:31] --------------------------------------------------------------------------------
[2025-08-11 07:48:31] 💸 COST BREAKDOWN:
[2025-08-11 07:48:31]    Current Task Cost: $0.000252
[2025-08-11 07:48:31]    Previous Task Cost: $0.000000
[2025-08-11 07:48:31]    Operation: APPENDED
[2025-08-11 07:48:31]    Reason: New task ID - appending cost to existing user total
[2025-08-11 07:48:31] --------------------------------------------------------------------------------
[2025-08-11 07:48:31] 📊 USER COST CHANGES:
[2025-08-11 07:48:31]    Original User Cost: $402.930350
[2025-08-11 07:48:31]    New User Cost: $402.930602
[2025-08-11 07:48:31]    User Cost Delta: $+0.000252
[2025-08-11 07:48:31]    📈 APPEND LOGIC: $402.930350 + $0.000252 = $402.930602
[2025-08-11 07:48:31] --------------------------------------------------------------------------------
[2025-08-11 07:48:31] Starting cost update in thread Thread-17 (_worker_loop)
[2025-08-11 07:48:31] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0005918}, Total cost: 0.0005918
[2025-08-11 07:48:31] [DEBUG] Starting session tracking for task_id: cg0afa38e3
[2025-08-11 07:48:31] [DEBUG] Updating session cost for task_id: cg0afa38e3
[2025-08-11 07:48:31] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0005918}
[2025-08-11 07:48:31] [DEBUG] Total cost: 0.0005918
[2025-08-11 07:48:31] Starting cost update in thread Thread-21 (_worker_loop)
[2025-08-11 07:48:31] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0006768}, Total cost: 0.0006768
[2025-08-11 07:48:31] [DEBUG] Starting session tracking for task_id: cg0afa38e3
[2025-08-11 07:48:31] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 07:48:31] [SUCCESS] Session cost REPLACED for task_id: cg0afa38e3
[2025-08-11 07:48:31] [SUCCESS] New total_cost: 0.0004565
[2025-08-11 07:48:31] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0004565}
[2025-08-11 07:48:31] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 07:48:31] 
[2025-08-11 07:48:31] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 07:48:31]    Total Users: 13
[2025-08-11 07:48:31]    Organization Cost: $885.982361
[2025-08-11 07:48:31] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:31] 🆕 Creating new project entry for Project ID: 90315
[2025-08-11 07:48:31] 
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m07:48:31 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[2025-08-11 07:48:31] ================================================================================
[2025-08-11 07:48:31] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 07:48:31] ================================================================================
[2025-08-11 07:48:31] 📋 Task ID: cg0afa38e3
[2025-08-11 07:48:31] 🏗️ Project ID: 90315
[2025-08-11 07:48:31] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:31] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:31] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:31] 🔍 Task Exists in Tracking: False
[2025-08-11 07:48:31] ✅ Actually New Task: True
[2025-08-11 07:48:31] --------------------------------------------------------------------------------
[2025-08-11 07:48:31] 💸 COST BREAKDOWN:
[2025-08-11 07:48:31]    Current Task Cost: $0.000168
[2025-08-11 07:48:31]    Previous Task Cost: $0.000000
[2025-08-11 07:48:31]    Operation: APPENDED
[2025-08-11 07:48:31]    Reason: New task ID - appending cost to existing user total
[2025-08-11 07:48:31] --------------------------------------------------------------------------------
[2025-08-11 07:48:31] 📊 USER COST CHANGES:
[2025-08-11 07:48:31]    Original User Cost: $402.930350
[2025-08-11 07:48:31]    New User Cost: $402.930518
[2025-08-11 07:48:31]    User Cost Delta: $+0.000168
[2025-08-11 07:48:31]    📈 APPEND LOGIC: $402.930350 + $0.000168 = $402.930518
[2025-08-11 07:48:31] --------------------------------------------------------------------------------
[2025-08-11 07:48:31] [SUCCESS] Session metadata updated for task_id: cg0afa38e3
[2025-08-11 07:48:31] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 07:48:31] [SUCCESS] Session cost tracking updated for task_id: cg0afa38e3
[2025-08-11 07:48:31] [SUCCESS] Costs REPLACED - Total: 0.0004565, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0004565}
[2025-08-11 07:48:31] [DEBUG] Updating session cost for task_id: cg0afa38e3
[2025-08-11 07:48:31] \00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00\00[DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0006768}
[2025-08-11 07:48:31] [DEBUG] Total cost: 0.0006768
[2025-08-11 07:48:31] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 07:48:31] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 7, 48, 31, 569730), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0006768}, 'total_cost': 0.0006768}
[2025-08-11 07:48:31] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 07:48:31] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 7, 48, 31, 572618), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0005918}, 'total_cost': 0.0005918}
[2025-08-11 07:48:31] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 07:48:31] [SUCCESS] Session cost REPLACED for task_id: cg0afa38e3
[2025-08-11 07:48:31] [SUCCESS] New total_cost: 0.0006768
[2025-08-11 07:48:31] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0006768}
[2025-08-11 07:48:31] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 07:48:31] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 07:48:31] [SUCCESS] Session cost REPLACED for task_id: cg0afa38e3
[2025-08-11 07:48:31] [SUCCESS] New total_cost: 0.0005918
[2025-08-11 07:48:31] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0005918}
[2025-08-11 07:48:31] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 07:48:31] 2025-08-11T07:48:31.599166+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:31.599159+00:00
[2025-08-11 07:48:31] 💾 LLM Costs Document Updated Successfully
[2025-08-11 07:48:31] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 07:48:31]    LLM Costs - User Cost: $402.930602
[2025-08-11 07:48:31]    LLM Costs - Organization Cost: $885.982613
[2025-08-11 07:48:31] --------------------------------------------------------------------------------
[2025-08-11 07:48:31] 🏢 ORGANIZATION COSTS:
[2025-08-11 07:48:31]    Original Org Cost: $885.982361
[2025-08-11 07:48:31]    New Org Cost: $885.982613
[2025-08-11 07:48:31]    Org Cost Delta: $+0.000252
[2025-08-11 07:48:31] --------------------------------------------------------------------------------
[2025-08-11 07:48:31] 📊 B2B COST UPDATE STATUS:
[2025-08-11 07:48:31]    LLM Costs Updated: ✅ YES
[2025-08-11 07:48:31]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 07:48:31] ================================================================================
[2025-08-11 07:48:31] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 07:48:31] [SUCCESS] Session metadata updated for task_id: cg0afa38e3
[2025-08-11 07:48:31] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 07:48:31] [SUCCESS] Session cost tracking updated for task_id: cg0afa38e3
[2025-08-11 07:48:31] [SUCCESS] Costs REPLACED - Total: 0.0005918, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0005918}
[2025-08-11 07:48:31] 2025-08-11T07:48:31.630144+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:31.630138+00:00
[2025-08-11 07:48:31] [SUCCESS] Session metadata updated for task_id: cg0afa38e3
[2025-08-11 07:48:31] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 07:48:31] [SUCCESS] Session cost tracking updated for task_id: cg0afa38e3
[2025-08-11 07:48:31] [SUCCESS] Costs REPLACED - Total: 0.0006768, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0006768}
[2025-08-11 07:48:31] 2025-08-11T07:48:31.631500+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:31.631495+00:00
[2025-08-11 07:48:31] 💾 LLM Costs Document Updated Successfully
[2025-08-11 07:48:31] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 07:48:31]    LLM Costs - User Cost: $402.930676
[2025-08-11 07:48:31]    LLM Costs - Organization Cost: $885.982687
[2025-08-11 07:48:31] --------------------------------------------------------------------------------
[2025-08-11 07:48:31] 🏢 ORGANIZATION COSTS:
[2025-08-11 07:48:31]    Original Org Cost: $885.982361
[2025-08-11 07:48:31]    New Org Cost: $885.982687
[2025-08-11 07:48:31]    Org Cost Delta: $+0.000326
[2025-08-11 07:48:31] --------------------------------------------------------------------------------
[2025-08-11 07:48:31] 📊 B2B COST UPDATE STATUS:
[2025-08-11 07:48:31]    LLM Costs Updated: ✅ YES
[2025-08-11 07:48:31]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 07:48:31] ================================================================================
[2025-08-11 07:48:31] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 07:48:31] 
[2025-08-11 07:48:31] 🔍 Starting B2B Cost Tracking
[2025-08-11 07:48:31] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:31] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:31] 💰 Total Cost: $0.000677
[2025-08-11 07:48:31] 🆔 Task ID: cg0afa38e3
[2025-08-11 07:48:31] 🏗️ Project ID: 90315
[2025-08-11 07:48:31] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:31] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0006768}
[2025-08-11 07:48:31] 
[2025-08-11 07:48:31] 🔍 Starting B2B Cost Tracking
[2025-08-11 07:48:31] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:31] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:31] 💰 Total Cost: $0.000592
[2025-08-11 07:48:31] 🆔 Task ID: cg0afa38e3
[2025-08-11 07:48:31] 🏗️ Project ID: 90315
[2025-08-11 07:48:31] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:31] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0005918}
[2025-08-11 07:48:31] 
[2025-08-11 07:48:31] 🔍 Starting B2B Cost Tracking
[2025-08-11 07:48:31] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:31] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:31] 💰 Total Cost: $0.000456
[2025-08-11 07:48:31] 🆔 Task ID: cg0afa38e3
[2025-08-11 07:48:31] 🏗️ Project ID: 90315
[2025-08-11 07:48:31] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:31] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0004565}
[2025-08-11 07:48:31] 
[2025-08-11 07:48:31] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 07:48:31]    Total Users: 13
[2025-08-11 07:48:31]    Organization Cost: $885.982687
[2025-08-11 07:48:31] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:31] 🏗️ Existing project entry found for Project ID: 90315
[2025-08-11 07:48:31] 
[2025-08-11 07:48:31] ================================================================================
[2025-08-11 07:48:31] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 07:48:31] ================================================================================
[2025-08-11 07:48:31] 📋 Task ID: cg0afa38e3
[2025-08-11 07:48:32] 🏗️ Project ID: 90315
[2025-08-11 07:48:32] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:32] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:32] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:32] 🔍 Task Exists in Tracking: True
[2025-08-11 07:48:32] ✅ Actually New Task: False
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 💸 COST BREAKDOWN:
[2025-08-11 07:48:32]    Current Task Cost: $0.000677
[2025-08-11 07:48:32]    Previous Task Cost: $0.000326
[2025-08-11 07:48:32]    Operation: REPLACED
[2025-08-11 07:48:32]    Reason: Continuing same task - replacing previous cost of $0.000326
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 📊 USER COST CHANGES:
[2025-08-11 07:48:32]    Original User Cost: $402.930676
[2025-08-11 07:48:32]    New User Cost: $402.931027
[2025-08-11 07:48:32]    User Cost Delta: $+0.000351
[2025-08-11 07:48:32]    🔄 REPLACE LOGIC: $402.930676 - $0.000326 + $0.000677 = $402.931027
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 
[2025-08-11 07:48:32] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 07:48:32]    Total Users: 13
[2025-08-11 07:48:32]    Organization Cost: $885.982687
[2025-08-11 07:48:32] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:32] 🏗️ Existing project entry found for Project ID: 90315
[2025-08-11 07:48:32] 
[2025-08-11 07:48:32] ================================================================================
[2025-08-11 07:48:32] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 07:48:32] ================================================================================
[2025-08-11 07:48:32] 📋 Task ID: cg0afa38e3
[2025-08-11 07:48:32] 🏗️ Project ID: 90315
[2025-08-11 07:48:32] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:32] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:32] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:32] 🔍 Task Exists in Tracking: True
[2025-08-11 07:48:32] ✅ Actually New Task: False
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 💸 COST BREAKDOWN:
[2025-08-11 07:48:32]    Current Task Cost: $0.000456
[2025-08-11 07:48:32]    Previous Task Cost: $0.000326
[2025-08-11 07:48:32]    Operation: REPLACED
[2025-08-11 07:48:32]    Reason: Continuing same task - replacing previous cost of $0.000326
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 📊 USER COST CHANGES:
[2025-08-11 07:48:32]    Original User Cost: $402.930676
[2025-08-11 07:48:32]    New User Cost: $402.930807
[2025-08-11 07:48:32]    User Cost Delta: $+0.000131
[2025-08-11 07:48:32]    🔄 REPLACE LOGIC: $402.930676 - $0.000326 + $0.000456 = $402.930807
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 💾 LLM Costs Document Updated Successfully
[2025-08-11 07:48:32] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 07:48:32]    LLM Costs - User Cost: $402.930518
[2025-08-11 07:48:32]    LLM Costs - Organization Cost: $885.982529
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 🏢 ORGANIZATION COSTS:
[2025-08-11 07:48:32]    Original Org Cost: $885.982361
[2025-08-11 07:48:32]    New Org Cost: $885.982529
[2025-08-11 07:48:32]    Org Cost Delta: $+0.000168
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 📊 B2B COST UPDATE STATUS:
[2025-08-11 07:48:32]    LLM Costs Updated: ✅ YES
[2025-08-11 07:48:32]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 07:48:32] ================================================================================
[2025-08-11 07:48:32] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 07:48:32] 
[2025-08-11 07:48:32] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 07:48:32]    Total Users: 13
[2025-08-11 07:48:32]    Organization Cost: $885.982687
[2025-08-11 07:48:32] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:32] 🏗️ Existing project entry found for Project ID: 90315
[2025-08-11 07:48:32] 
[2025-08-11 07:48:32] ================================================================================
[2025-08-11 07:48:32] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 07:48:32] ================================================================================
[2025-08-11 07:48:32] 📋 Task ID: cg0afa38e3
[2025-08-11 07:48:32] 🏗️ Project ID: 90315
[2025-08-11 07:48:32] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:32] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:32] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:32] 🔍 Task Exists in Tracking: True
[2025-08-11 07:48:32] ✅ Actually New Task: False
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 💸 COST BREAKDOWN:
[2025-08-11 07:48:32]    Current Task Cost: $0.000592
[2025-08-11 07:48:32]    Previous Task Cost: $0.000326
[2025-08-11 07:48:32]    Operation: REPLACED
[2025-08-11 07:48:32]    Reason: Continuing same task - replacing previous cost of $0.000326
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 📊 USER COST CHANGES:
[2025-08-11 07:48:32]    Original User Cost: $402.930676
[2025-08-11 07:48:32]    New User Cost: $402.930942
[2025-08-11 07:48:32]    User Cost Delta: $+0.000266
[2025-08-11 07:48:32]    🔄 REPLACE LOGIC: $402.930676 - $0.000326 + $0.000592 = $402.930942
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] Metrics submitted successfully: 1 datapoints
[2025-08-11 07:48:32] 💾 LLM Costs Document Updated Successfully
[2025-08-11 07:48:32] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 07:48:32]    LLM Costs - User Cost: $402.930806
[2025-08-11 07:48:32]    LLM Costs - Organization Cost: $885.982817
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 🏢 ORGANIZATION COSTS:
[2025-08-11 07:48:32]    Original Org Cost: $885.982687
[2025-08-11 07:48:32]    New Org Cost: $885.982817
[2025-08-11 07:48:32]    Org Cost Delta: $+0.000130
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 📊 B2B COST UPDATE STATUS:
[2025-08-11 07:48:32]    LLM Costs Updated: ✅ YES
[2025-08-11 07:48:32]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 07:48:32] ================================================================================
[2025-08-11 07:48:32] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 07:48:32] Starting task start callback in thread _task_start_callback_thread_daemon_139841679021888
[2025-08-11 07:48:32] NoneType: None
[2025-08-11 07:48:32] 
[2025-08-11 07:48:32] 💾 LLM Costs Document Updated Successfully
[2025-08-11 07:48:32] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 07:48:32]    LLM Costs - User Cost: $402.931027
[2025-08-11 07:48:32]    LLM Costs - Organization Cost: $885.983038
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 🏢 ORGANIZATION COSTS:
[2025-08-11 07:48:32]    Original Org Cost: $885.982687
[2025-08-11 07:48:32]    New Org Cost: $885.983038
[2025-08-11 07:48:32]    Org Cost Delta: $+0.000351
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 📊 B2B COST UPDATE STATUS:
[2025-08-11 07:48:32]    LLM Costs Updated: ✅ YES
[2025-08-11 07:48:32]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 07:48:32] ================================================================================
[2025-08-11 07:48:32] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 07:48:32] Task start callback thread _task_start_callback_thread_daemon_139841679021888 completed
[2025-08-11 07:48:32] 💾 LLM Costs Document Updated Successfully
[2025-08-11 07:48:32] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 07:48:32]    LLM Costs - User Cost: $402.930942
[2025-08-11 07:48:32]    LLM Costs - Organization Cost: $885.982953
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 🏢 ORGANIZATION COSTS:
[2025-08-11 07:48:32]    Original Org Cost: $885.982687
[2025-08-11 07:48:32]    New Org Cost: $885.982953
[2025-08-11 07:48:32]    Org Cost Delta: $+0.000266
[2025-08-11 07:48:32] --------------------------------------------------------------------------------
[2025-08-11 07:48:32] 📊 B2B COST UPDATE STATUS:
[2025-08-11 07:48:32]    LLM Costs Updated: ✅ YES
[2025-08-11 07:48:32]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 07:48:32] ================================================================================
[2025-08-11 07:48:32] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 07:48:32] 2025-08-11T07:48:32.927709+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:32.927701+00:00
[2025-08-11 07:48:32] Message added ChatMessage(id=f5107b43-9c06-41fa-b87c-70c93bdab132, type = llm content= 
[2025-08-11 07:48:32] 
[2025-08-11 07:48:32]  # 🤖 Welcome to the Kavia Code Assistant!
[2025-08-11 07:48:32] 
[2025-08-11 07:48:32] I'm here to help you build a **simple Todo application** with a React-based frontend. 🚀
[2025-08-11 07:48:32] 
[2025-08-11 07:48:32] The app will allow users to add, update, complete, and delete tasks through an intuitive UI. Your project already has a defined layout and style preferences, and it's ready for coding!
[2025-08-11 07:48:32] 
[2025-08-11 07:48:32] ## What would you like to do next?
[2025-08-11 07:48:32] 
[2025-08-11 07:48:32] • 💻 **Write all the code for this**
[2025-08-11 07:48:32] • 📋 **Create a plan**
[2025-08-11 07:48:32] • 🧪 **Build a test suite**
[2025-08-11 07:48:32] • ▶️ **Run the tests**
[2025-08-11 07:48:32] • 📝 **Create architecture documents**
[2025-08-11 07:48:32] • ❓ **Answer questions about the codebase / architecture**
[2025-08-11 07:48:32] • 🆘 **Help**
[2025-08-11 07:48:32] 
[2025-08-11 07:48:32] Feel free to choose what you'd like to do next!, status=MessageStatus.PENDING), parent_id=None), metadata=None), timestamp=2025-08-11 07:48:32.906909),requires_resolution=True), extra={})
[2025-08-11 07:48:32] PHASE FIRST MESSAGE, START TIME:  2025-08-11T07:48:32.927923+00:00
[92m07:48:33 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:code_generation_core_agent.agents.framework.knowledge_embeddings:Lazily initializing Milvus client for todo_frontend
INFO:pymilvus.orm.connections:Pass in the local path /home/<USER>/workspace/code-generation/simple-todo-manager-90315-90324/.knowledge/.vector_db/milvus.db, and run it using milvus-lite
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m07:48:34 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
DEBUG:pymilvus.milvus_client.milvus_client:Created new connection using: 93b8fdd2c82848f29413053b7c544598
DEBUG:pymilvus.milvus_client.milvus_client:Successfully created collection: file_search_terms_embeddings
DEBUG:pymilvus.milvus_client.milvus_client:Successfully created an index on collection: file_search_terms_embeddings
INFO:code_generation_core_agent.agents.framework.knowledge_embeddings:Search terms embeddings file_search_terms_embeddings loaded: {'state': <LoadState: Loaded>}
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
[92m07:48:36 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m07:48:36 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m07:48:36 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[2025-08-11 07:48:36] Message dict {'id': 'f5107b43-9c06-41fa-b87c-70c93bdab132', 'content': " \n\n # 🤖 Welcome to the Kavia Code Assistant!\n\nI'm here to help you build a **simple Todo application** with a React-based frontend. 🚀\n\nThe app will allow users to add, update, complete, and delete tasks through an intuitive UI. Your project already has a defined layout and style preferences, and it's ready for coding!\n\n## What would you like to do next?\n\n• 💻 **Write all the code for this**\n• 📋 **Create a plan**\n• 🧪 **Build a test suite**\n• ▶️ **Run the tests**\n• 📝 **Create architecture documents**\n• ❓ **Answer questions about the codebase / architecture**\n• 🆘 **Help**\n\nFeel free to choose what you'd like to do next!", 'msg_type': 'llm', 'timestamp': '2025-08-11T07:48:32.906909+00:00', 'status': 'pending', 'requires_resolution': True, 'resolution_id': None, 'parent_id': None, 'metadata': None, 'attachments': [], 'extra': {}}
[2025-08-11 07:48:36] Cost update thread Thread-20 (_worker_loop) completed
[2025-08-11 07:48:36] 2025-08-11T07:48:32.958720+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:32.958714+00:00
[2025-08-11 07:48:36] Message needs response ChatMessage(id=f5107b43-9c06-41fa-b87c-70c93bdab132, type = llm content= 
[2025-08-11 07:48:36] 
[2025-08-11 07:48:36]  # 🤖 Welcome to the Kavia Code Assistant!
[2025-08-11 07:48:36] 
[2025-08-11 07:48:36] I'm here to help you build a **simple Todo application** with a React-based frontend. 🚀
[2025-08-11 07:48:36] 
[2025-08-11 07:48:36] The app will allow users to add, update, complete, and delete tasks through an intuitive UI. Your project already has a defined layout and style preferences, and it's ready for coding!
[2025-08-11 07:48:36] 
[2025-08-11 07:48:36] ## What would you like to do next?
[2025-08-11 07:48:36] 
[2025-08-11 07:48:36] • 💻 **Write all the code for this**
[2025-08-11 07:48:36] • 📋 **Create a plan**
[2025-08-11 07:48:36] • 🧪 **Build a test suite**
[2025-08-11 07:48:36] • ▶️ **Run the tests**
[2025-08-11 07:48:36] • 📝 **Create architecture documents**
[2025-08-11 07:48:36] • ❓ **Answer questions about the codebase / architecture**
[2025-08-11 07:48:36] • 🆘 **Help**
[2025-08-11 07:48:36] 
[2025-08-11 07:48:36] Feel free to choose what you'd like to do next!, status=MessageStatus.NEEDS_RESPONSE), parent_id=None), metadata=None), timestamp=2025-08-11 07:48:32.906909+00:00),requires_resolution=True), extra={})
[2025-08-11 07:48:36] Cost update thread Thread-16 (_worker_loop) completed
[2025-08-11 07:48:36] Cost update thread Thread-15 (_worker_loop) completed
[2025-08-11 07:48:36] Cost update thread Thread-19 (_worker_loop) completed
[2025-08-11 07:48:36] Cost update thread Thread-21 (_worker_loop) completed
[2025-08-11 07:48:36] Cost update thread Thread-18 (_worker_loop) completed
[2025-08-11 07:48:36] Starting cost update in thread Thread-20 (_worker_loop)
[2025-08-11 07:48:36] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0008053, 'TaskExecutionAgent': 0.0005639}, Total cost: 0.0013692
[2025-08-11 07:48:36] [DEBUG] Starting session tracking for task_id: cg0afa38e3
[2025-08-11 07:48:36] [DEBUG] Updating session cost for task_id: cg0afa38e3
[2025-08-11 07:48:36] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0008053, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:36] [DEBUG] Total cost: 0.0013692
[2025-08-11 07:48:36] Cost update thread Thread-17 (_worker_loop) completed
[2025-08-11 07:48:36] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 07:48:36] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 7, 48, 34, 24679), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0008053, 'TaskExecutionAgent': 0.0005639}, 'total_cost': 0.0013692}
[2025-08-11 07:48:36] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 07:48:36] [SUCCESS] Session cost REPLACED for task_id: cg0afa38e3
[2025-08-11 07:48:36] [SUCCESS] New total_cost: 0.0013692
[2025-08-11 07:48:36] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0008053, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:36] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 07:48:36] [SUCCESS] Session metadata updated for task_id: cg0afa38e3
[2025-08-11 07:48:36] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 07:48:36] [SUCCESS] Session cost tracking updated for task_id: cg0afa38e3
[2025-08-11 07:48:36] [SUCCESS] Costs REPLACED - Total: 0.0013692, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0008053, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:36] 2025-08-11T07:48:34.048641+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:34.048636+00:00
[2025-08-11 07:48:36] 
[2025-08-11 07:48:36] 🔍 Starting B2B Cost Tracking
[2025-08-11 07:48:36] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:36] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:36] 💰 Total Cost: $0.001369
[2025-08-11 07:48:36] 🆔 Task ID: cg0afa38e3
[2025-08-11 07:48:36] 🏗️ Project ID: 90315
[2025-08-11 07:48:36] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:36] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0008053, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:36] 
[2025-08-11 07:48:36] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 07:48:36]    Total Users: 13
[2025-08-11 07:48:36]    Organization Cost: $885.983038
[2025-08-11 07:48:36] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:36] 🏗️ Existing project entry found for Project ID: 90315
[2025-08-11 07:48:36] 
[2025-08-11 07:48:36] ================================================================================
[2025-08-11 07:48:36] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 07:48:36] ================================================================================
[2025-08-11 07:48:36] 📋 Task ID: cg0afa38e3
[2025-08-11 07:48:36] 🏗️ Project ID: 90315
[2025-08-11 07:48:36] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:36] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:36] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:36] 🔍 Task Exists in Tracking: True
[2025-08-11 07:48:36] ✅ Actually New Task: False
[2025-08-11 07:48:36] --------------------------------------------------------------------------------
[2025-08-11 07:48:36] 💸 COST BREAKDOWN:
[2025-08-11 07:48:36]    Current Task Cost: $0.001369
[2025-08-11 07:48:36]    Previous Task Cost: $0.000677
[2025-08-11 07:48:36]    Operation: REPLACED
[2025-08-11 07:48:36]    Reason: Continuing same task - replacing previous cost of $0.000677
[2025-08-11 07:48:36] --------------------------------------------------------------------------------
[2025-08-11 07:48:36] 📊 USER COST CHANGES:
[2025-08-11 07:48:36]    Original User Cost: $402.931027
[2025-08-11 07:48:36]    New User Cost: $402.931719
[2025-08-11 07:48:36]    User Cost Delta: $+0.000692
[2025-08-11 07:48:36]    🔄 REPLACE LOGIC: $402.931027 - $0.000677 + $0.001369 = $402.931719
[2025-08-11 07:48:36] --------------------------------------------------------------------------------
[2025-08-11 07:48:36] 💾 LLM Costs Document Updated Successfully
[2025-08-11 07:48:36] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 07:48:36]    LLM Costs - User Cost: $402.931719
[2025-08-11 07:48:36]    LLM Costs - Organization Cost: $885.983730
[2025-08-11 07:48:36] --------------------------------------------------------------------------------
[2025-08-11 07:48:36] 🏢 ORGANIZATION COSTS:
[2025-08-11 07:48:36]    Original Org Cost: $885.983038
[2025-08-11 07:48:36]    New Org Cost: $885.983730
[2025-08-11 07:48:36]    Org Cost Delta: $+0.000692
[2025-08-11 07:48:36] --------------------------------------------------------------------------------
[2025-08-11 07:48:36] 📊 B2B COST UPDATE STATUS:
[2025-08-11 07:48:36]    LLM Costs Updated: ✅ YES
[2025-08-11 07:48:36]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 07:48:36] ================================================================================
[2025-08-11 07:48:36] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 07:48:36] Cost update thread Thread-20 (_worker_loop) completed
[92m07:48:36 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m07:48:36 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m07:48:38 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[2025-08-11 07:48:38] Starting cost update in thread Thread-15 (_worker_loop)
[2025-08-11 07:48:38] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0008743, 'TaskExecutionAgent': 0.0005639}, Total cost: 0.0014382
[2025-08-11 07:48:38] [DEBUG] Starting session tracking for task_id: cg0afa38e3
[2025-08-11 07:48:38] [DEBUG] Updating session cost for task_id: cg0afa38e3
[2025-08-11 07:48:38] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0008743, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:38] [DEBUG] Total cost: 0.0014382
[2025-08-11 07:48:38] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 07:48:38] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 7, 48, 36, 866142), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0008743, 'TaskExecutionAgent': 0.0005639}, 'total_cost': 0.0014382}
[2025-08-11 07:48:38] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 07:48:38] [SUCCESS] Session cost REPLACED for task_id: cg0afa38e3
[2025-08-11 07:48:38] [SUCCESS] New total_cost: 0.0014382
[2025-08-11 07:48:38] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0008743, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:38] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 07:48:38] [SUCCESS] Session metadata updated for task_id: cg0afa38e3
[2025-08-11 07:48:38] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 07:48:38] [SUCCESS] Session cost tracking updated for task_id: cg0afa38e3
[2025-08-11 07:48:38] [SUCCESS] Costs REPLACED - Total: 0.0014382, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0008743, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:38] 2025-08-11T07:48:36.889830+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:36.889825+00:00
[2025-08-11 07:48:38] 
[2025-08-11 07:48:38] 🔍 Starting B2B Cost Tracking
[2025-08-11 07:48:38] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:38] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:38] 💰 Total Cost: $0.001438
[2025-08-11 07:48:38] 🆔 Task ID: cg0afa38e3
[2025-08-11 07:48:38] 🏗️ Project ID: 90315
[2025-08-11 07:48:38] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:38] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0008743, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:38] 
[2025-08-11 07:48:38] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 07:48:38]    Total Users: 13
[2025-08-11 07:48:38]    Organization Cost: $885.983730
[2025-08-11 07:48:38] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:38] 🏗️ Existing project entry found for Project ID: 90315
[2025-08-11 07:48:38] 
[2025-08-11 07:48:38] ================================================================================
[2025-08-11 07:48:38] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 07:48:38] ================================================================================
[2025-08-11 07:48:38] 📋 Task ID: cg0afa38e3
[2025-08-11 07:48:38] 🏗️ Project ID: 90315
[2025-08-11 07:48:38] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:38] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:38] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:38] 🔍 Task Exists in Tracking: True
[2025-08-11 07:48:38] ✅ Actually New Task: False
[2025-08-11 07:48:38] --------------------------------------------------------------------------------
[2025-08-11 07:48:38] 💸 COST BREAKDOWN:
[2025-08-11 07:48:38]    Current Task Cost: $0.001438
[2025-08-11 07:48:38]    Previous Task Cost: $0.001369
[2025-08-11 07:48:38]    Operation: REPLACED
[2025-08-11 07:48:38]    Reason: Continuing same task - replacing previous cost of $0.001369
[2025-08-11 07:48:38] --------------------------------------------------------------------------------
[2025-08-11 07:48:38] 📊 USER COST CHANGES:
[2025-08-11 07:48:38]    Original User Cost: $402.931719
[2025-08-11 07:48:38]    New User Cost: $402.931788
[2025-08-11 07:48:38]    User Cost Delta: $+0.000069
[2025-08-11 07:48:38]    🔄 REPLACE LOGIC: $402.931719 - $0.001369 + $0.001438 = $402.931788
[2025-08-11 07:48:38] --------------------------------------------------------------------------------
[2025-08-11 07:48:38] 💾 LLM Costs Document Updated Successfully
[2025-08-11 07:48:38] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 07:48:38]    LLM Costs - User Cost: $402.931788
[2025-08-11 07:48:38]    LLM Costs - Organization Cost: $885.983799
[2025-08-11 07:48:38] --------------------------------------------------------------------------------
[2025-08-11 07:48:38] 🏢 ORGANIZATION COSTS:
[2025-08-11 07:48:38]    Original Org Cost: $885.983730
[2025-08-11 07:48:38]    New Org Cost: $885.983799
[2025-08-11 07:48:38]    Org Cost Delta: $+0.000069
[2025-08-11 07:48:38] --------------------------------------------------------------------------------
[2025-08-11 07:48:38] 📊 B2B COST UPDATE STATUS:
[2025-08-11 07:48:38]    LLM Costs Updated: ✅ YES
[2025-08-11 07:48:38]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 07:48:38] ================================================================================
[2025-08-11 07:48:38] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 07:48:38] Cost update thread Thread-15 (_worker_loop) completed
[2025-08-11 07:48:38] Starting cost update in thread Thread-21 (_worker_loop)
[2025-08-11 07:48:38] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0009427, 'TaskExecutionAgent': 0.0005639}, Total cost: 0.0015065999999999999
[2025-08-11 07:48:38] [DEBUG] Starting session tracking for task_id: cg0afa38e3
[2025-08-11 07:48:38] [DEBUG] Updating session cost for task_id: cg0afa38e3
[2025-08-11 07:48:38] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0009427, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:38] [DEBUG] Total cost: 0.0015065999999999999
[2025-08-11 07:48:38] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 07:48:38] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 7, 48, 38, 220259), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0009427, 'TaskExecutionAgent': 0.0005639}, 'total_cost': 0.0015065999999999999}
[2025-08-11 07:48:38] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 07:48:38] [SUCCESS] Session cost REPLACED for task_id: cg0afa38e3
[2025-08-11 07:48:38] [SUCCESS] New total_cost: 0.0015065999999999999
[2025-08-11 07:48:38] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0009427, 'TaskExecutionAgent': 0.0005639}
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m07:48:39 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[2025-08-11 07:48:39] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 07:48:39] [SUCCESS] Session metadata updated for task_id: cg0afa38e3
[2025-08-11 07:48:39] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 07:48:39] [SUCCESS] Session cost tracking updated for task_id: cg0afa38e3
[2025-08-11 07:48:39] [SUCCESS] Costs REPLACED - Total: 0.0015065999999999999, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0009427, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:39] 2025-08-11T07:48:38.270293+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:38.270284+00:00
[2025-08-11 07:48:39] 
[2025-08-11 07:48:39] 🔍 Starting B2B Cost Tracking
[2025-08-11 07:48:39] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:39] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:39] 💰 Total Cost: $0.001507
[2025-08-11 07:48:39] 🆔 Task ID: cg0afa38e3
[2025-08-11 07:48:39] 🏗️ Project ID: 90315
[2025-08-11 07:48:39] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:39] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0009427, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:39] 
[2025-08-11 07:48:39] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 07:48:39]    Total Users: 13
[2025-08-11 07:48:39]    Organization Cost: $885.983799
[2025-08-11 07:48:39] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:39] 🏗️ Existing project entry found for Project ID: 90315
[2025-08-11 07:48:39] 
[2025-08-11 07:48:39] ================================================================================
[2025-08-11 07:48:39] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 07:48:39] ================================================================================
[2025-08-11 07:48:39] 📋 Task ID: cg0afa38e3
[2025-08-11 07:48:39] 🏗️ Project ID: 90315
[2025-08-11 07:48:39] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:39] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:39] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:39] 🔍 Task Exists in Tracking: True
[2025-08-11 07:48:39] ✅ Actually New Task: False
[2025-08-11 07:48:39] --------------------------------------------------------------------------------
[2025-08-11 07:48:39] 💸 COST BREAKDOWN:
[2025-08-11 07:48:39]    Current Task Cost: $0.001507
[2025-08-11 07:48:39]    Previous Task Cost: $0.001438
[2025-08-11 07:48:39]    Operation: REPLACED
[2025-08-11 07:48:39]    Reason: Continuing same task - replacing previous cost of $0.001438
[2025-08-11 07:48:39] --------------------------------------------------------------------------------
[2025-08-11 07:48:39] 📊 USER COST CHANGES:
[2025-08-11 07:48:39]    Original User Cost: $402.931788
[2025-08-11 07:48:39]    New User Cost: $402.931857
[2025-08-11 07:48:39]    User Cost Delta: $+0.000069
[2025-08-11 07:48:39]    🔄 REPLACE LOGIC: $402.931788 - $0.001438 + $0.001507 = $402.931857
[2025-08-11 07:48:39] --------------------------------------------------------------------------------
[2025-08-11 07:48:39] 💾 LLM Costs Document Updated Successfully
[2025-08-11 07:48:39] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 07:48:39]    LLM Costs - User Cost: $402.931857
[2025-08-11 07:48:39]    LLM Costs - Organization Cost: $885.983868
[2025-08-11 07:48:39] --------------------------------------------------------------------------------
[2025-08-11 07:48:39] 🏢 ORGANIZATION COSTS:
[2025-08-11 07:48:39]    Original Org Cost: $885.983799
[2025-08-11 07:48:39]    New Org Cost: $885.983868
[2025-08-11 07:48:39]    Org Cost Delta: $+0.000069
[2025-08-11 07:48:39] --------------------------------------------------------------------------------
[2025-08-11 07:48:39] 📊 B2B COST UPDATE STATUS:
[2025-08-11 07:48:39]    LLM Costs Updated: ✅ YES
[2025-08-11 07:48:39]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 07:48:39] ================================================================================
[2025-08-11 07:48:39] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 07:48:39] Cost update thread Thread-21 (_worker_loop) completed
[2025-08-11 07:48:39] Starting cost update in thread Thread-16 (_worker_loop)
[2025-08-11 07:48:39] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0010798, 'TaskExecutionAgent': 0.0005639}, Total cost: 0.0016437
[2025-08-11 07:48:39] [DEBUG] Starting session tracking for task_id: cg0afa38e3
[2025-08-11 07:48:39] [DEBUG] Updating session cost for task_id: cg0afa38e3
[2025-08-11 07:48:39] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0010798, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:39] [DEBUG] Total cost: 0.0016437
[2025-08-11 07:48:39] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 07:48:39] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 7, 48, 39, 479307), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0010798, 'TaskExecutionAgent': 0.0005639}, 'total_cost': 0.0016437}
[2025-08-11 07:48:39] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 07:48:39] [SUCCESS] Session cost REPLACED for task_id: cg0afa38e3
[2025-08-11 07:48:39] [SUCCESS] New total_cost: 0.0016437
[2025-08-11 07:48:39] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0010798, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:39] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 07:48:39] [SUCCESS] Session metadata updated for task_id: cg0afa38e3
[2025-08-11 07:48:39] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 07:48:39] [SUCCESS] Session cost tracking updated for task_id: cg0afa38e3
[2025-08-11 07:48:39] [SUCCESS] Costs REPLACED - Total: 0.0016437, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0010798, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:39] 2025-08-11T07:48:39.538400+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:39.538396+00:00
[2025-08-11 07:48:39] 
[2025-08-11 07:48:39] 🔍 Starting B2B Cost Tracking
[2025-08-11 07:48:39] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:39] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:39] 💰 Total Cost: $0.001644
[2025-08-11 07:48:39] 🆔 Task ID: cg0afa38e3
[2025-08-11 07:48:39] 🏗️ Project ID: 90315
[2025-08-11 07:48:39] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:39] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0010798, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:48:39] 
[2025-08-11 07:48:39] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 07:48:39]    Total Users: 13
[2025-08-11 07:48:39]    Organization Cost: $885.983868
[2025-08-11 07:48:39] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:39] 🏗️ Existing project entry found for Project ID: 90315
[2025-08-11 07:48:39] 
[2025-08-11 07:48:39] ================================================================================
[2025-08-11 07:48:39] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 07:48:39] ================================================================================
[2025-08-11 07:48:39] 📋 Task ID: cg0afa38e3
[2025-08-11 07:48:39] 🏗️ Project ID: 90315
[2025-08-11 07:48:39] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:48:39] 🏢 Organization ID: rdk7542
[2025-08-11 07:48:39] 🆕 Is New Task (Parameter): False
[2025-08-11 07:48:39] 🔍 Task Exists in Tracking: True
[2025-08-11 07:48:39] ✅ Actually New Task: False
[2025-08-11 07:48:39] --------------------------------------------------------------------------------
[2025-08-11 07:48:39] 💸 COST BREAKDOWN:
[2025-08-11 07:48:39]    Current Task Cost: $0.001644
[2025-08-11 07:48:39]    Previous Task Cost: $0.001507
[2025-08-11 07:48:39]    Operation: REPLACED
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m07:48:41 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
[2025-08-11 07:49:09]    Reason: Continuing same task - replacing previous cost of $0.001507
[2025-08-11 07:49:09] --------------------------------------------------------------------------------
[2025-08-11 07:49:09] 📊 USER COST CHANGES:
[2025-08-11 07:49:09]    Original User Cost: $402.931857
[2025-08-11 07:49:09]    New User Cost: $402.931994
[2025-08-11 07:49:09]    User Cost Delta: $+0.000137
[2025-08-11 07:49:09]    🔄 REPLACE LOGIC: $402.931857 - $0.001507 + $0.001644 = $402.931994
[2025-08-11 07:49:09] --------------------------------------------------------------------------------
[2025-08-11 07:49:09] 💾 LLM Costs Document Updated Successfully
[2025-08-11 07:49:09] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 07:49:09]    LLM Costs - User Cost: $402.931994
[2025-08-11 07:49:09]    LLM Costs - Organization Cost: $885.984005
[2025-08-11 07:49:09] --------------------------------------------------------------------------------
[2025-08-11 07:49:09] 🏢 ORGANIZATION COSTS:
[2025-08-11 07:49:09]    Original Org Cost: $885.983868
[2025-08-11 07:49:09]    New Org Cost: $885.984005
[2025-08-11 07:49:09]    Org Cost Delta: $+0.000137
[2025-08-11 07:49:09] --------------------------------------------------------------------------------
[2025-08-11 07:49:09] 📊 B2B COST UPDATE STATUS:
[2025-08-11 07:49:09]    LLM Costs Updated: ✅ YES
[2025-08-11 07:49:09]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 07:49:09] ================================================================================
[2025-08-11 07:49:09] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 07:49:09] Cost update thread Thread-16 (_worker_loop) completed
[2025-08-11 07:49:09] Starting cost update in thread Thread-19 (_worker_loop)
[2025-08-11 07:49:09] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0012655, 'TaskExecutionAgent': 0.0005639}, Total cost: 0.0018294
[2025-08-11 07:49:09] [DEBUG] Starting session tracking for task_id: cg0afa38e3
[2025-08-11 07:49:09] [DEBUG] Updating session cost for task_id: cg0afa38e3
[2025-08-11 07:49:09] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0012655, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:49:09] [DEBUG] Total cost: 0.0018294
[2025-08-11 07:49:09] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 07:49:09] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 7, 48, 41, 140606), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0012655, 'TaskExecutionAgent': 0.0005639}, 'total_cost': 0.0018294}
[2025-08-11 07:49:09] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 07:49:09] [SUCCESS] Session cost REPLACED for task_id: cg0afa38e3
[2025-08-11 07:49:09] [SUCCESS] New total_cost: 0.0018294
[2025-08-11 07:49:09] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0012655, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:49:09] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 07:49:09] [SUCCESS] Session metadata updated for task_id: cg0afa38e3
[2025-08-11 07:49:09] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 07:49:09] [SUCCESS] Session cost tracking updated for task_id: cg0afa38e3
[2025-08-11 07:49:09] [SUCCESS] Costs REPLACED - Total: 0.0018294, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0012655, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:49:09] 2025-08-11T07:48:41.157267+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:48:41.157263+00:00
[2025-08-11 07:49:09] 
[2025-08-11 07:49:09] 🔍 Starting B2B Cost Tracking
[2025-08-11 07:49:09] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:49:09] 🏢 Organization ID: rdk7542
[2025-08-11 07:49:09] 💰 Total Cost: $0.001829
[2025-08-11 07:49:09] 🆔 Task ID: cg0afa38e3
[2025-08-11 07:49:09] 🏗️ Project ID: 90315
[2025-08-11 07:49:09] 🆕 Is New Task (Parameter): False
[2025-08-11 07:49:09] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0012655, 'TaskExecutionAgent': 0.0005639}
[2025-08-11 07:49:09] 
[2025-08-11 07:49:09] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 07:49:09]    Total Users: 13
[2025-08-11 07:49:09]    Organization Cost: $885.984005
[2025-08-11 07:49:09] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:49:09] 🏗️ Existing project entry found for Project ID: 90315
[2025-08-11 07:49:09] 
[2025-08-11 07:49:09] ================================================================================
[2025-08-11 07:49:09] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 07:49:09] ================================================================================
[2025-08-11 07:49:09] 📋 Task ID: cg0afa38e3
[2025-08-11 07:49:09] 🏗️ Project ID: 90315
[2025-08-11 07:49:09] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 07:49:09] 🏢 Organization ID: rdk7542
[2025-08-11 07:49:09] 🆕 Is New Task (Parameter): False
[2025-08-11 07:49:09] 🔍 Task Exists in Tracking: True
[2025-08-11 07:49:09] ✅ Actually New Task: False
[2025-08-11 07:49:09] --------------------------------------------------------------------------------
[2025-08-11 07:49:09] 💸 COST BREAKDOWN:
[2025-08-11 07:49:09]    Current Task Cost: $0.001829
[2025-08-11 07:49:09]    Previous Task Cost: $0.001644
[2025-08-11 07:49:09]    Operation: REPLACED
[2025-08-11 07:49:09]    Reason: Continuing same task - replacing previous cost of $0.001644
[2025-08-11 07:49:09] --------------------------------------------------------------------------------
[2025-08-11 07:49:09] 📊 USER COST CHANGES:
[2025-08-11 07:49:09]    Original User Cost: $402.931994
[2025-08-11 07:49:09]    New User Cost: $402.932179
[2025-08-11 07:49:09]    User Cost Delta: $+0.000185
[2025-08-11 07:49:09]    🔄 REPLACE LOGIC: $402.931994 - $0.001644 + $0.001829 = $402.932179
[2025-08-11 07:49:09] --------------------------------------------------------------------------------
[2025-08-11 07:49:09] 💾 LLM Costs Document Updated Successfully
[2025-08-11 07:49:09] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 07:49:09]    LLM Costs - User Cost: $402.932179
[2025-08-11 07:49:09]    LLM Costs - Organization Cost: $885.984190
[2025-08-11 07:49:09] --------------------------------------------------------------------------------
[2025-08-11 07:49:09] 🏢 ORGANIZATION COSTS:
[2025-08-11 07:49:09]    Original Org Cost: $885.984005
[2025-08-11 07:49:09]    New Org Cost: $885.984190
[2025-08-11 07:49:09]    Org Cost Delta: $+0.000185
[2025-08-11 07:49:09] --------------------------------------------------------------------------------
[2025-08-11 07:49:09] 📊 B2B COST UPDATE STATUS:
[2025-08-11 07:49:09]    LLM Costs Updated: ✅ YES
[2025-08-11 07:49:09]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 07:49:09] ================================================================================
[2025-08-11 07:49:09] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 07:49:09] Cost update thread Thread-19 (_worker_loop) completed
[2025-08-11 07:49:09] 2025-08-11T07:49:09.726602+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:09.726597+00:00
[2025-08-11 07:49:09] *****Calling html_with_assset****
[2025-08-11 07:49:09] *******************************************************
[2025-08-11 07:49:09] 2025-08-11T07:49:09.727717+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:09] 2025-08-11T07:49:09.727744+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 07:49:09] 2025-08-11T07:49:09.727779+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:09] 2025-08-11T07:49:09.727832+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:09] 2025-08-11T07:49:09.727849+00:00 HTML file not found in fallback location either
[2025-08-11 07:49:09] 2025-08-11T07:49:09.799893+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90315/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 07:49:09] 2025-08-11T07:49:09.828987+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:09.828981+00:00
[2025-08-11 07:49:09] *****Calling html_with_assset****
[2025-08-11 07:49:09] *******************************************************
[2025-08-11 07:49:28] 2025-08-11T07:49:09.830058+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:09.830131+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 07:49:28] 2025-08-11T07:49:09.830158+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:09.830334+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:09.830450+00:00 HTML file not found in fallback location either
[2025-08-11 07:49:28] 2025-08-11T07:49:09.885208+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90315/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 07:49:28] 2025-08-11T07:49:09.931799+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:09.931792+00:00
[2025-08-11 07:49:28] *****Calling html_with_assset****
[2025-08-11 07:49:28] *******************************************************
[2025-08-11 07:49:28] 2025-08-11T07:49:09.932761+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:09.932782+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 07:49:28] 2025-08-11T07:49:09.932827+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:09.933229+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:09.933253+00:00 HTML file not found in fallback location either
[2025-08-11 07:49:28] 2025-08-11T07:49:09.997394+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90315/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 07:49:28] 2025-08-11T07:49:10.034864+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:10.034858+00:00
[2025-08-11 07:49:28] *****Calling html_with_assset****
[2025-08-11 07:49:28] *******************************************************
[2025-08-11 07:49:28] 2025-08-11T07:49:10.036304+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:10.036328+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 07:49:28] 2025-08-11T07:49:10.036351+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:10.036404+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:10.036425+00:00 HTML file not found in fallback location either
[2025-08-11 07:49:28] 2025-08-11T07:49:10.097446+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90315/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 07:49:28] 2025-08-11T07:49:10.144047+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:10.144041+00:00
[2025-08-11 07:49:28] *****Calling html_with_assset****
[2025-08-11 07:49:28] *******************************************************
[2025-08-11 07:49:28] 2025-08-11T07:49:10.145748+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:10.145767+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 07:49:28] 2025-08-11T07:49:10.145785+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:10.145830+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:10.145848+00:00 HTML file not found in fallback location either
[2025-08-11 07:49:28] 2025-08-11T07:49:10.204197+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90315/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 07:49:28] 2025-08-11T07:49:10.248604+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:10.248597+00:00
[2025-08-11 07:49:28] *****Calling html_with_assset****
[2025-08-11 07:49:28] *******************************************************
[2025-08-11 07:49:28] 2025-08-11T07:49:10.250678+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:10.250752+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 07:49:28] 2025-08-11T07:49:10.250779+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:10.250834+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:10.250855+00:00 HTML file not found in fallback location either
[2025-08-11 07:49:28] 2025-08-11T07:49:10.306973+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90315/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 07:49:28] 2025-08-11T07:49:12.260224+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:12.260218+00:00
[2025-08-11 07:49:28] Received generate_figma_screen_code request for screen: TODO PAGE
[2025-08-11 07:49:28] Screen ID: 9:680
[2025-08-11 07:49:28] 2025-08-11T07:49:12.269255+00:00 Looking for screen file: 9:680.json
[2025-08-11 07:49:28] 2025-08-11T07:49:12.271385+00:00 Screen file not found: /efs/rdk7542/90315/workspace/temp-attachments/9:680.json
[2025-08-11 07:49:28] 2025-08-11T07:49:12.274186+00:00 Available JSON files in temp-attachments: ['screen_9:680.json', 'screen_9:780.json', 'screen_9:757.json', 'screen_9:739.json']
[2025-08-11 07:49:28] 2025-08-11T07:49:12.274223+00:00 Copied 0 file(s) for screen 'TODO PAGE' (ID: 9:680)
[2025-08-11 07:49:28] 2025-08-11T07:49:12.318524+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90315/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 07:49:28] 2025-08-11T07:49:12.331750+00:00 Successfully synced attachments folder from /efs/rdk7542/90315/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 07:49:28] Getting NodeDB connection for tenant: developrdk7542
[2025-08-11 07:49:28] query---> MATCH (n) WHERE ID(n) = $node_id SET n.Manifest = $Manifest RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties 
[2025-08-11 07:49:28] params--->> {'node_id': 90315, 'Manifest': '{"overview": {"project_name": "simple_todo_app", "description": "A simple Todo app that allows users to add, update, complete, and delete tasks. The application features an intuitive UI for managing tasks and supports basic CRUD operations.", "third_party_services": [], "env": {}}, "containers": [{"container_name": "todo_frontend", "description": "User interface for managing todo tasks.", "interfaces": "UI accessible through a web browser.", "container_type": "frontend", "dependent_containers": ["todo_database"], "workspace": "simple-todo-manager-90315-90324", "container_root": "simple-todo-manager-90315-90324/todo_frontend", "port": 3000, "framework": "react", "type": "", "buildCommand": "npm install && npx tsc --noEmit && npm test -- --ci", "startCommand": "PORT=<port> HOST=<host> BROWSER=none npm start", "installCommand": "npm install", "lintCommand": "./../.init/.linter.sh", "generateOpenapiCommand": "", "container_details": {"features": ["add tasks", "view task list", "edit tasks", "mark tasks as completed", "delete tasks"], "colors": {"primary": "#1976d2", "secondary": "#388e3c", "accent": "#e64a19"}, "theme": "light", "layout_description": "Simple centered layout with an input form at the top and a vertical list of todos below.", "style": "minimalistic"}, "lintConfig": "", "routes": [], "apiSpec": "", "auth": null, "schema": "", "migrations": "", "seed": "", "env": {}}]}'}
[2025-08-11 07:49:28] 2025-08-11T07:49:28.307428+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:28.307422+00:00
[2025-08-11 07:49:28] *****Calling html_with_assset****
[2025-08-11 07:49:28] *******************************************************
[2025-08-11 07:49:28] 2025-08-11T07:49:28.309547+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:28.309575+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 07:49:28] 2025-08-11T07:49:28.309614+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:28.310190+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:28] 2025-08-11T07:49:28.310222+00:00 HTML file not found in fallback location either
[2025-08-11 07:49:28] 2025-08-11T07:49:28.385258+00:00 Successfully synced attachments folder from /efs/rdk7542/90315/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 07:49:28] 2025-08-11T07:49:28.388554+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90315/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 07:49:28] 2025-08-11T07:49:28.411469+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:28.411463+00:00
[2025-08-11 07:49:28] *****Calling html_with_assset****
[2025-08-11 07:49:28] *******************************************************
[2025-08-11 07:49:28] 2025-08-11T07:49:28.417231+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:28.417260+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 07:49:39] 2025-08-11T07:49:28.417330+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:28.424433+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:28.424696+00:00 HTML file not found in fallback location either
[2025-08-11 07:49:39] 2025-08-11T07:49:28.501235+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90315/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 07:49:39] 2025-08-11T07:49:28.510388+00:00 Successfully synced attachments folder from /efs/rdk7542/90315/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 07:49:39] 2025-08-11T07:49:28.526480+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:28.526474+00:00
[2025-08-11 07:49:39] *****Calling html_with_assset****
[2025-08-11 07:49:39] *******************************************************
[2025-08-11 07:49:39] 2025-08-11T07:49:28.527286+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:28.527305+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 07:49:39] 2025-08-11T07:49:28.527331+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:28.527393+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:28.527419+00:00 HTML file not found in fallback location either
[2025-08-11 07:49:39] 2025-08-11T07:49:28.579713+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90315/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 07:49:39] 2025-08-11T07:49:28.581266+00:00 Successfully synced attachments folder from /efs/rdk7542/90315/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 07:49:39] 2025-08-11T07:49:28.629550+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:28.629544+00:00
[2025-08-11 07:49:39] *****Calling html_with_assset****
[2025-08-11 07:49:39] *******************************************************
[2025-08-11 07:49:39] 2025-08-11T07:49:28.630481+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:28.630497+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 07:49:39] 2025-08-11T07:49:28.630516+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:28.630569+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:28.630588+00:00 HTML file not found in fallback location either
[2025-08-11 07:49:39] 2025-08-11T07:49:28.702408+00:00 Successfully synced attachments folder from /efs/rdk7542/90315/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 07:49:39] 2025-08-11T07:49:28.703755+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90315/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 07:49:39] 2025-08-11T07:49:28.895563+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:28.895557+00:00
[2025-08-11 07:49:39] *****Calling html_with_assset****
[2025-08-11 07:49:39] *******************************************************
[2025-08-11 07:49:39] 2025-08-11T07:49:28.908318+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:28.908342+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 07:49:39] 2025-08-11T07:49:28.908365+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:28.908417+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:28.908436+00:00 HTML file not found in fallback location either
[2025-08-11 07:49:39] 2025-08-11T07:49:28.967361+00:00 Successfully synced attachments folder from /efs/rdk7542/90315/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 07:49:39] 2025-08-11T07:49:28.979004+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90315/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 07:49:39] 2025-08-11T07:49:29.028619+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:29.028612+00:00
[2025-08-11 07:49:39] *****Calling html_with_assset****
[2025-08-11 07:49:39] *******************************************************
[2025-08-11 07:49:39] 2025-08-11T07:49:29.038425+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:29.038470+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 07:49:39] 2025-08-11T07:49:29.038560+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:29.038649+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 07:49:39] 2025-08-11T07:49:29.038672+00:00 HTML file not found in fallback location either
[2025-08-11 07:49:39] 2025-08-11T07:49:29.077903+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90315/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 07:49:39] 2025-08-11T07:49:29.122354+00:00 Successfully synced attachments folder from /efs/rdk7542/90315/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 07:49:39] 2025-08-11T07:49:30.137111+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:30.137105+00:00
[2025-08-11 07:49:39] Received generate_figma_screen_code request for screen: TODO PAGE
[2025-08-11 07:49:39] Screen ID: 9:680
[2025-08-11 07:49:39] 2025-08-11T07:49:30.138592+00:00 Looking for screen file: 9:680.json
[2025-08-11 07:49:39] 2025-08-11T07:49:30.138642+00:00 Screen file not found: /efs/rdk7542/90315/workspace/temp-attachments/9:680.json
[2025-08-11 07:49:39] 2025-08-11T07:49:30.139826+00:00 Available JSON files in temp-attachments: ['screen_9:680.json', 'screen_9:780.json', 'screen_9:757.json', 'screen_9:739.json']
[2025-08-11 07:49:39] 2025-08-11T07:49:30.139853+00:00 Copied 0 file(s) for screen 'TODO PAGE' (ID: 9:680)
[2025-08-11 07:49:39] 2025-08-11T07:49:30.224743+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90315/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 07:49:39] 2025-08-11T07:49:30.243390+00:00 Successfully synced attachments folder from /efs/rdk7542/90315/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 07:49:39] NoneType: None
[2025-08-11 07:49:39] 
[2025-08-11 07:49:39] 2025-08-11T07:49:39.670803+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:39.670798+00:00
[2025-08-11 07:49:39] Message added ChatMessage(id=c9c47991-5565-46b1-a379-bc5f10dfc9af, type = command content=Preview for todo_frontend is now available at: https://vscode-internal-23169-dev.dev01.cloud.kavia.ai:4000/preview.html, status=MessageStatus.COMPLETED), parent_id=None), metadata=None), timestamp=2025-08-11 07:49:39.670398),requires_resolution=False), extra={})
[2025-08-11 07:49:39] Message dict {'id': 'c9c47991-5565-46b1-a379-bc5f10dfc9af', 'content': 'Preview for todo_frontend is now available at: https://vscode-internal-23169-dev.dev01.cloud.kavia.ai:4000/preview.html', 'msg_type': 'command', 'timestamp': '2025-08-11T07:49:39.670398+00:00', 'status': 'completed', 'requires_resolution': False, 'resolution_id': None, 'parent_id': None, 'metadata': None, 'attachments': [], 'extra': {}}
[2025-08-11 07:49:39] 2025-08-11T07:49:39.673542+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:39.673537+00:00
[2025-08-11 07:49:39] Message added ChatMessage(id=03c5b44c-7ebe-48fc-bee5-992adc23c1f0, type = system content=Resolved user message, status=MessageStatus.COMPLETED), parent_id=c9c47991-5565-46b1-a379-bc5f10dfc9af), metadata=None), timestamp=2025-08-11 07:49:39.673147),requires_resolution=False), extra={})
[2025-08-11 07:49:39] Message dict {'id': '03c5b44c-7ebe-48fc-bee5-992adc23c1f0', 'content': 'Resolved user message', 'msg_type': 'system', 'timestamp': '2025-08-11T07:49:39.673147+00:00', 'status': 'completed', 'requires_resolution': False, 'resolution_id': None, 'parent_id': 'c9c47991-5565-46b1-a379-bc5f10dfc9af', 'metadata': None, 'attachments': None, 'extra': {}}
[2025-08-11 07:49:39] 2025-08-11T07:49:39.676563+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:39.676556+00:00
[2025-08-11 07:49:39] Message resolved ChatMessage(id=c9c47991-5565-46b1-a379-bc5f10dfc9af, type = command content=Preview for todo_frontend is now available at: https://vscode-internal-23169-dev.dev01.cloud.kavia.ai:4000/preview.html, status=MessageStatus.RESOLVED), parent_id=None), metadata=None), timestamp=2025-08-11 07:49:39.670398+00:00),requires_resolution=False), extra={})
[2025-08-11 07:49:39] Starting app state callback in thread _app_state_callback_thread_daemon_139839963915840
[2025-08-11 07:49:39] 🎯 First time app state callback triggered - initiating commit changes
INFO:     Started server process [543]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:4000 (Press CTRL+C to quit)
[2025-08-11 07:49:40] ✅ First-time commit changes initiated successfully
[2025-08-11 07:49:40] 2025-08-11T07:49:39.687892+00:00 Activity updated for task cg0afa38e3 at 2025-08-11 07:49:39.687884+00:00
[2025-08-11 07:49:40] Preview URL and state https://vscode-internal-23169-dev.dev01.cloud.kavia.ai:4000/preview.html running
[2025-08-11 07:49:40] Found 1 containers for task cg0afa38e3
[2025-08-11 07:49:40] Containers [{'status': 'running', 'timestamp': '2025-08-11T07:49:39.625676', 'url': 'https://vscode-internal-23169-dev.dev01.cloud.kavia.ai:3000/preview.html', 'api_route': '', 'db_env_vars': None, 'error': None, 'failed_dependencies': [], 'warning': None, 'name': 'todo_frontend', 'container_type': 'web', 'framework': 'react'}]
[2025-08-11 07:49:40] App state callback thread _app_state_callback_thread_daemon_139839963915840 completed
[2025-08-11 07:49:40] INFO:     10.1.8.62:56480 - "HEAD /preview.html HTTP/1.1" 200 OK
[2025-08-11 07:49:40] INFO:     10.1.8.62:56464 - "HEAD /preview.html HTTP/1.1" 200 OK