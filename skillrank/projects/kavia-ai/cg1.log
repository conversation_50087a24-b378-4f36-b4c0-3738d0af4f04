/usr/local/lib/python3.11/site-packages/pymilvus/client/__init__.py:6: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
/usr/local/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:app.connection.mongo_client:MongoDB connection successful
INFO:websocket:Websocket connected
/usr/local/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
[2025-08-11 09:39:18] Is codegen :  False
[2025-08-11 09:39:18] 2025-08-11T09:39:11.246365+00:00 🔄 Starting Google credentials setup in daemon thread...
[2025-08-11 09:39:18] 2025-08-11T09:39:11.246432+00:00 Google credentials setup started in daemon thread 2025-08-11T09:39:11.246443+00:00
[2025-08-11 09:39:18] 2025-08-11T09:39:11.246454+00:00 Current PYTHONPATH: /app
[2025-08-11 09:39:18] 2025-08-11T09:39:11.246469+00:00 Current sys.path: ['/app/app/batch_jobs', '/app', '/usr/local/lib/python311.zip', '/usr/local/lib/python3.11', '/usr/local/lib/python3.11/lib-dynload', '/usr/local/lib/python3.11/site-packages', '/usr/local/lib/python3.11/site-packages/setuptools/_vendor', '/app']
[2025-08-11 09:39:18] 2025-08-11T09:39:11.246734+00:00 🔄 Fetching Google credentials from secrets...
[2025-08-11 09:39:18] Celery app configured with url amqps://admin:<EMAIL>:5671
[2025-08-11 09:39:18] base_path /app/app/core/datamodel.json
[2025-08-11 09:39:18] 
[2025-08-11 09:39:18] 2025-08-11T09:39:11.982656+00:00 📋 Type of credentials: <class 'str'>
[2025-08-11 09:39:18] 2025-08-11T09:39:11.984917+00:00 ✅ GOOGLE_APPLICATION_CREDENTIALS set to: /app/google_credentials.json
[2025-08-11 09:39:18] 2025-08-11T09:39:11.984941+00:00 ✅ Google credentials daemon setup completed: /app/google_credentials.json
[2025-08-11 09:39:18] 2025-08-11T09:39:13.270368+00:00 PHASE 1, START TIME:  2025-08-11T09:39:13.270383+00:00
[2025-08-11 09:39:18] 2025-08-11T09:39:13.271214+00:00 {'project_id': 90358, 'task_id': 'cgb11bdb92', 'llm_model': 'bedrock/converse/us.anthropic.claude-3-7-sonnet-20250219-v1:0', 'tenant_id': 'rdk7542', 'agent_name': 'CodeGeneration', 'platform': 'web', 'user_id': '14d8e428-5001-701f-dee2-9f12f5ce8c56', 'architecture_id': None}
[2025-08-11 09:39:18] 2025-08-11T09:39:13.271249+00:00 True
[2025-08-11 09:39:18] 2025-08-11T09:39:13.271261+00:00 ['screen', '-L', '-Logfile', '/tmp/kavia/workspace/logs/cgb11bdb92.log', '-dmS', 'cgb11bdb92', 'python', 'app/batch_jobs/jobs.py', '--input_args', '{"project_id": 90358, "task_id": "cgb11bdb92", "llm_model": "bedrock/converse/us.anthropic.claude-3-7-sonnet-20250219-v1:0", "tenant_id": "rdk7542", "agent_name": "CodeGeneration", "platform": "web", "user_id": "14d8e428-5001-701f-dee2-9f12f5ce8c56", "architecture_id": null}', '--stage', 'dev']
[2025-08-11 09:39:18] 2025-08-11T09:39:13.271274+00:00 Tenant_id for code generation rdk7542
[2025-08-11 09:39:18] Getting NodeDB connection for tenant: developrdk7542
[2025-08-11 09:39:18] Starting connection attempt...
[2025-08-11 09:39:18] Attempt 1 of 5
[2025-08-11 09:39:18] Connection and registration successful
[2025-08-11 09:39:18] 2025-08-11T09:39:13.667854+00:00 {'_id': 'cgb11bdb92', 'application_preivew_url': 'https://vscode-internal-39097-dev.dev01.cloud.kavia.ai', 'container_id': [90367], 'container_ids': [90367], 'context': {}, 'description': 'A simple todo application allowing users to add, view, edit, and delete their tasks.', 'encrypted_scm_id': '', 'iframe': 'https://vscode-internal-39097-dev.dev01.cloud.kavia.ai/?folder=/home/<USER>/workspace/code-generation/', 'ip': '127.0.0.1', 'job_id': 'cgb11bdb92', 'llm_model': 'bedrock/converse/us.anthropic.claude-3-7-sonnet-20250219-v1:0', 'new_repo_creation': True, 'params': '?project_id=90358&task_id=cgb11bdb92&stage=dev&llm_model=bedrock/converse/us.anthropic.claude-3-7-sonnet-20250219-v1:0&tenant_id=rdk7542&platform=web&pod_id=39097&user_id=14d8e428-5001-701f-dee2-9f12f5ce8c56', 'platform': 'web', 'pod_id': '39097', 'pod_name': '39097', 'ports': [{'3000': 'https://vscode-internal-39097-dev.dev01.cloud.kavia.ai/?folder=/home/<USER>/workspace/code-generation/'}, {'5000': 'https://vscode-internal-39097-dev.dev01.cloud.kavia.ai/?folder=/home/<USER>/workspace/code-generation/'}, {'8000': 'https://vscode-internal-39097-dev.dev01.cloud.kavia.ai/?folder=/home/<USER>/workspace/code-generation/'}], 'project_id': 90358, 'resume': False, 'session_name': 'Simple Todo List', 'stage': 'dev', 'start_time': '2025-08-11T09:39:03.897024+00:00', 'status': 'SUBMITTED', 'user_id': '14d8e428-5001-701f-dee2-9f12f5ce8c56'}
[2025-08-11 09:39:18] Host with scheme extracted: https://vscode-internal-39097-dev.dev01.cloud.kavia.ai
[2025-08-11 09:39:18] 2025-08-11T09:39:13.667966+00:00 Host for iframe https://vscode-internal-39097-dev.dev01.cloud.kavia.ai
[2025-08-11 09:39:18] 2025-08-11T09:39:13.668020+00:00 agent details {"llm_model":"gpt-4.1","agent_name":"CodeGeneration"}
[2025-08-11 09:39:18] 2025-08-11T09:39:13.668044+00:00 Starting code generation job
[2025-08-11 09:39:18] 2025-08-11T09:39:13.668050+00:00 Executing code generation for project 90358
[2025-08-11 09:39:18] [2025-08-11T09:39:13.668118+00:00] Starting document restore for resume: cgb11bdb92
[2025-08-11 09:39:18] [2025-08-11T09:39:13.691862+00:00] Restoring docs from MongoDB for task: cgb11bdb92
[2025-08-11 09:39:18] [2025-08-11T09:39:13.694521+00:00] No documents found in MongoDB for task cgb11bdb92, skipping kavia-docs creation
[2025-08-11 09:39:18] 2025-08-11T09:39:13.694574+00:00 ℹ️ No documents found in MongoDB, skipping kavia-docs creation
[2025-08-11 09:39:18] 2025-08-11T09:39:13.857287+00:00 container_ids [90367]
[2025-08-11 09:39:18] 2025-08-11T09:39:13.857308+00:00 {}
[2025-08-11 09:39:18] 2025-08-11T09:39:13.859930+00:00 🔒 Acquired repository lock for project 90358, container 90367
[2025-08-11 09:39:18] 2025-08-11T09:39:13.864186+00:00 Creating repository in workspace
[2025-08-11 09:39:18] 2025-08-11T09:39:18.422147+00:00 Repository meta data
[2025-08-11 09:39:18] 2025-08-11T09:39:18.422172+00:00 {'service': 'github', 'repositoryName': 'simple-todo-list-90358-90367', 'repositoryId': '1035918895', 'cloneUrlHttp': 'https://github.com/kavia-common/simple-todo-list-90358-90367.git', 'cloneUrlSsh': '**************:kavia-common/simple-todo-list-90358-90367.git', 'organization': 'kavia-common', 'access_token_path': 'settings', 'repositoryStatus': 'initialized'}
[2025-08-11 09:39:18] query---> MATCH (n) WHERE ID(n) = $node_id SET n.repositories = $repositories RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties 
[2025-08-11 09:39:18] params--->> {'node_id': 90358, 'repositories': '{"90367": {"service": "github", "repositoryName": "simple-todo-list-90358-90367", "repositoryId": "1035918895", "cloneUrlHttp": "https://github.com/kavia-common/simple-todo-list-90358-90367.git", "cloneUrlSsh": "**************:kavia-common/simple-todo-list-90358-90367.git", "organization": "kavia-common", "access_token_path": "settings", "repositoryStatus": "initialized"}}'}
[2025-08-11 09:39:18] 2025-08-11T09:39:18.430992+00:00 2025-08-11T09:39:18.431012+00:00 Started background clone thread for repository: simple-todo-list-90358-90367
[2025-08-11 09:39:18] 2025-08-11T09:39:18.431028+00:00 🔓 Released repository lock for project 90358, container 90367
[2025-08-11 09:39:18] query---> MATCH (n) WHERE ID(n) = $node_id SET n.repositories = $repositories RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties 
[2025-08-11 09:39:18] params--->> {'node_id': 90358, 'repositories': '{"90367": {"service": "github", "repositoryName": "simple-todo-list-90358-90367", "repositoryId": "1035918895", "cloneUrlHttp": "https://github.com/kavia-common/simple-todo-list-90358-90367.git", "cloneUrlSsh": "**************:kavia-common/simple-todo-list-90358-90367.git", "organization": "kavia-common", "access_token_path": "settings", "repositoryStatus": "initialized", "access_token": "*********************************************************************************************", "container_id": "90367", "container_name": "todo_frontend"}}'}
[2025-08-11 09:39:18] 2025-08-11T09:39:18.431759+00:00 🔒 Added PID 476 to filesystem lock for simple-todo-list-90358-90367
[2025-08-11 09:39:18] 2025-08-11T09:39:18.431935+00:00 🔄 Starting background clone for repository: simple-todo-list-90358-90367
[2025-08-11 09:39:18] Successfully saved manifest to /home/<USER>/workspace/code-generation/.project_manifest.yaml
[2025-08-11 09:39:18] overview_data --------------------------------------------------------------------------> {'project_name': 'todo_app', 'description': 'A simple todo application allowing users to add, view, edit, and delete their tasks.', 'env': {}}
[2025-08-11 09:39:18] 2025-08-11T09:39:18.466307+00:00 [] [Container({'container_name': 'todo_frontend', 'description': 'Provides the user interface for managing todo items.', 'interfaces': 'User interface for creating, viewing, editing, and deleting todos', 'container_type': <ContainerType.FRONTEND: 'frontend'>, 'dependent_containers': ['todo_database'], 'workspace': 'simple-todo-list-90358-90367', 'container_root': 'simple-todo-list-90358-90367/todo_frontend', 'port': 3000, 'framework': 'react', 'type': '', 'buildCommand': '', 'startCommand': '', 'installCommand': '', 'lintCommand': '', 'generateOpenapiCommand': '', 'container_details': {'features': ['Add todo item', 'View todo list', 'Edit todo item', 'Delete todo item', 'Mark item as completed'], 'colors': {'primary': '#1e90ff', 'secondary': '#ff9800', 'accent': '#4caf50'}, 'theme': 'light', 'layout_description': 'A central list displaying todos with input at the top or modal for adding/editing, and buttons for actions next to each item.', 'style': 'minimalistic'}, 'lintConfig': '', 'routes': [], 'apiSpec': '', 'auth': None, 'schema': '', 'migrations': '', 'seed': '', 'env': {}})]
[2025-08-11 09:39:18] [2025-08-11T09:39:18.471240+00:00] Starting document restore for resume: cgb11bdb92
[2025-08-11 09:39:18] [2025-08-11T09:39:18.471270+00:00] Restoring docs from MongoDB for task: cgb11bdb92
[2025-08-11 09:39:18] [2025-08-11T09:39:18.474469+00:00] No documents found in MongoDB for task cgb11bdb92, skipping kavia-docs creation
[2025-08-11 09:39:18] 2025-08-11T09:39:18.474533+00:00 ℹ️ No documents found in MongoDB, skipping kavia-docs creation
[2025-08-11 09:39:19] 2025-08-11T09:39:18.474548+00:00 Project Details:  {'Type': 'project', 'TempData': '{"overview": {"project_name": "todo_app", "description": "A simple todo application allowing users to add, view, edit, and delete their tasks.", "env": {}, "third_party_services": []}, "containers": [{"container_name": "todo_database", "description": "Stores todo items and related user data for the application.", "interfaces": "SQL queries, REST API (for CRUD operations via backend if added later)", "container_type": "database", "dependent_containers": [], "workspace": "todo_database_workspace", "ports": 5001, "framework": "SQLite", "type": "", "buildCommand": "", "startCommand": "", "lintCommand": "", "container_details": "Lightweight SQL-based database storing todo items with fields such as id, title, description, status, and timestamps.", "lintConfig": "", "routes": [], "apiSpec": "", "auth": null, "schema": "", "migrations": "", "seed": ""}, {"container_name": "todo_frontend", "description": "Provides the user interface for managing todo items.", "interfaces": "User interface for creating, viewing, editing, and deleting todos", "container_type": "frontend", "dependent_containers": ["todo_database"], "workspace": "todo_frontend_workspace", "ports": 3000, "framework": "React JS", "type": "", "buildCommand": "", "startCommand": "", "lintCommand": "", "container_details": {"features": ["Add todo item", "View todo list", "Edit todo item", "Delete todo item", "Mark item as completed"], "colors": {"primary": "#1e90ff", "secondary": "#ff9800", "accent": "#4caf50"}, "theme": "light", "layout_description": "A central list displaying todos with input at the top or modal for adding/editing, and buttons for actions next to each item.", "style": "minimalistic"}, "lintConfig": "", "routes": [], "apiSpec": "", "auth": null, "schema": "", "migrations": "", "seed": ""}]}', 'architecturePattern': '', 'manifest': "overview:\n  project_name: todo_app\n  description: A simple todo application allowing users to add, view, edit, and delete\n    their tasks.\n  third_party_services: []\n  env: {}\ncontainers:\n- container_name: todo_database\n  description: Stores todo items and related user data for the application.\n  interfaces: SQL queries, REST API (for CRUD operations via backend if added later)\n  container_type: database\n  dependent_containers: []\n  workspace: todo_database_workspace\n  container_root: todo_database_workspace/todo_database\n  port: ''\n  framework: SQLite\n  type: ''\n  buildCommand: ''\n  startCommand: ''\n  installCommand: ''\n  lintCommand: ''\n  generateOpenapiCommand: ''\n  container_details: Lightweight SQL-based database storing todo items with fields\n    such as id, title, description, status, and timestamps.\n  lintConfig: ''\n  routes: []\n  apiSpec: ''\n  auth: null\n  schema: ''\n  migrations: ''\n  seed: ''\n  env: {}\n- container_name: todo_frontend\n  description: Provides the user interface for managing todo items.\n  interfaces: User interface for creating, viewing, editing, and deleting todos\n  container_type: frontend\n  dependent_containers:\n  - todo_database\n  workspace: todo_frontend_workspace\n  container_root: todo_frontend_workspace/todo_frontend\n  port: ''\n  framework: React JS\n  type: ''\n  buildCommand: ''\n  startCommand: ''\n  installCommand: ''\n  lintCommand: ''\n  generateOpenapiCommand: ''\n  container_details:\n    features:\n    - Add todo item\n    - View todo list\n    - Edit todo item\n    - Delete todo item\n    - Mark item as completed\n    colors:\n      primary: '#1e90ff'\n      secondary: '#ff9800'\n      accent: '#4caf50'\n      background: '#ffffff'\n    theme: light\n    layout_description: A central list displaying todos with input at the top or modal\n      for adding/editing, and buttons for actions next to each item.\n    style: minimalistic\n  lintConfig: ''\n  routes: []\n  apiSpec: ''\n  auth: null\n  schema: ''\n  migrations: ''\n  seed: ''\n  env: {}\n", 'frontend': '{"container": {"features": [{"name": "Add todo item", "description": "Add todo item", "isEnabled": true}, {"name": "View todo list", "description": "View todo list", "isEnabled": true}, {"name": "Edit todo item", "description": "Edit todo item", "isEnabled": true}, {"name": "Delete todo item", "description": "Delete todo item", "isEnabled": true}, {"name": "Mark item as completed", "description": "Mark item as completed", "isEnabled": true}], "colors": {"primary": "#1e90ff", "secondary": "#ff9800", "accent": "#4caf50", "background": "#ffffff"}, "layoutDescription": "A central list displaying todos with input at the top or modal for adding/editing, and buttons for actions next to each item.", "framework": "React JS"}}', 'configuration_state': 'configured', 'Manifest': '{"overview": {"project_name": "todo_app", "description": "A simple todo application allowing users to add, view, edit, and delete their tasks.", "version": "1.0.0", "env": {}, "third_party_services": []}, "containers": [{"container_name": "todo_frontend", "description": "Provides the user interface for managing todo items.", "interfaces": "User interface for creating, viewing, editing, and deleting todos", "container_type": "frontend", "dependent_containers": ["todo_database"], "workspace": "todo_frontend_workspace", "ports": 3000, "framework": "react", "type": "", "buildCommand": "", "startCommand": "", "lintCommand": "", "container_details": {"features": ["Add todo item", "View todo list", "Edit todo item", "Delete todo item", "Mark item as completed"], "colors": {"primary": "#1e90ff", "secondary": "#ff9800", "accent": "#4caf50"}, "theme": "light", "layout_description": "A central list displaying todos with input at the top or modal for adding/editing, and buttons for actions next to each item.", "style": "minimalistic"}, "lintConfig": "", "routes": [], "apiSpec": "", "auth": null, "schema": "", "migrations": "", "seed": ""}]}', 'Tech_Stack': '{"frontend": ["React JS"], "backend": ["None"], "database": ["None"]}', 'work_item_type': 'project', 'created_at': '2025-08-11T09:37:38.434537+00:00', 'created_by': '14d8e428-5001-701f-dee2-9f12f5ce8c56', 'Layout_Description': 'A central list displaying todos with input at the top or modal for adding/editing, and buttons for actions next to each item.', 'Description': 'A simple todo application allowing users to add, view, edit, and delete their tasks.', 'is_active': True, 'frontend_framework': 'React JS', 'status': 'active', 'Colors': '{"primary": "#1e90ff", "secondary": "#ff9800", "accent": "#4caf50"}', 'Title': 'Simple Todo List', 'Features': '[{"id": "1", "name": "Add todo item", "description": "Add todo item", "isEnabled": true}, {"id": "2", "name": "View todo list", "description": "View todo list", "isEnabled": true}, {"id": "3", "name": "Edit todo item", "description": "Edit todo item", "isEnabled": true}, {"id": "4", "name": "Delete todo item", "description": "Delete todo item", "isEnabled": true}, {"id": "5", "name": "Mark item as completed", "description": "Mark item as completed", "isEnabled": true}]', 'Init_project_info': '{"id": "90358", "name": "todo_app", "description": "A simple todo application allowing users to add, view, edit, and delete their tasks.", "features": [{"id": "1", "name": "Add todo item", "description": "Add todo item", "isEnabled": true}, {"id": "2", "name": "View todo list", "description": "View todo list", "isEnabled": true}, {"id": "3", "name": "Edit todo item", "description": "Edit todo item", "isEnabled": true}, {"id": "4", "name": "Delete todo item", "description": "Delete todo item", "isEnabled": true}, {"id": "5", "name": "Mark item as completed", "description": "Mark item as completed", "isEnabled": true}], "techStack": {"frontend": ["React JS"], "backend": ["None"], "database": ["None"]}, "colors": {"primary": "#1e90ff", "secondary": "#ff9800", "accent": "#4caf50"}, "theme": "light", "estimatedTime": "1-2 weeks", "complexity": "simple", "layoutDescription": "A central list displaying todos with input at the top or modal for adding/editing, and buttons for actions next to each item.", "architecturePattern": null}', 'Theme': 'light', 'Complexity': 'simple', 'overview': '{"project_name": "todo_app", "description": "A simple todo application allowing users to add, view, edit, and delete their tasks."}', 'Estimated_Time': '1-2 weeks', 'database_framework': 'none', 'current_repositories': [{'service': 'github', 'repositoryName': 'simple-todo-list-90358-90367', 'repositoryId': '1035918895', 'cloneUrlHttp': 'https://github.com/kavia-common/simple-todo-list-90358-90367.git', 'cloneUrlSsh': '**************:kavia-common/simple-todo-list-90358-90367.git', 'organization': 'kavia-common', 'access_token_path': 'settings', 'repositoryStatus': 'initialized', 'access_token': '*********************************************************************************************', 'container_id': '90367', 'container_name': 'todo_frontend'}]}
[2025-08-11 09:39:19] 2025-08-11T09:39:18.475074+00:00 WorkItem Detais:  {'project_type': 'multi_container', 'project_name': 'todo_app', 'description': 'A simple todo application allowing users to add, view, edit, and delete their tasks.', 'env': {}, 'containers': [{'container_name': 'todo_frontend', 'platform': 'web', 'framework': 'react', 'description': 'Provides the user interface for managing todo items.', 'interfaces': 'User interface for creating, viewing, editing, and deleting todos', 'workspace': 'simple-todo-list-90358-90367', 'container_root': 'simple-todo-list-90358-90367/todo_frontend', 'dependencies': ['todo_database'], 'container_type': 'frontend', 'container_details': {'features': ['Add todo item', 'View todo list', 'Edit todo item', 'Delete todo item', 'Mark item as completed'], 'colors': {'primary': '#1e90ff', 'secondary': '#ff9800', 'accent': '#4caf50'}, 'theme': 'light', 'layout_description': 'A central list displaying todos with input at the top or modal for adding/editing, and buttons for actions next to each item.', 'style': 'minimalistic'}}], 'overview': {'project_name': 'todo_app', 'description': 'A simple todo application allowing users to add, view, edit, and delete their tasks.', 'env': {}, 'frontend_framework': 'react'}, '3rd_party_services': [], 'figma_components': '', 'manifest_path': '/home/<USER>/workspace/code-generation/.project_manifest.yaml'}
[2025-08-11 09:39:19] 2025-08-11T09:39:18.475138+00:00 USER ID 14d8e428-5001-701f-dee2-9f12f5ce8c56  Tenant ID rdk7542
[2025-08-11 09:39:19] 2025-08-11T09:39:18.475179+00:00 Platform is web and selected framework is : None
[2025-08-11 09:39:19]  ++ initialising new connection ++
[2025-08-11 09:39:19] 2025-08-11T09:39:18.840539+00:00 DEBUG - CodeGeneration current_repositories: [{'service': 'github', 'repositoryName': 'simple-todo-list-90358-90367', 'repositoryId': '1035918895', 'cloneUrlHttp': 'https://github.com/kavia-common/simple-todo-list-90358-90367.git', 'cloneUrlSsh': '**************:kavia-common/simple-todo-list-90358-90367.git', 'organization': 'kavia-common', 'access_token_path': 'settings', 'repositoryStatus': 'initialized', 'access_token': '*********************************************************************************************', 'container_id': '90367', 'container_name': 'todo_frontend'}]
[2025-08-11 09:39:19] 2025-08-11T09:39:18.840579+00:00 DEBUG - Selected repository_metadata: {'service': 'github', 'repositoryName': 'simple-todo-list-90358-90367', 'repositoryId': '1035918895', 'cloneUrlHttp': 'https://github.com/kavia-common/simple-todo-list-90358-90367.git', 'cloneUrlSsh': '**************:kavia-common/simple-todo-list-90358-90367.git', 'organization': 'kavia-common', 'access_token_path': 'settings', 'repositoryStatus': 'initialized', 'access_token': '*********************************************************************************************', 'container_id': '90367', 'container_name': 'todo_frontend'}
[2025-08-11 09:39:19] Sending message:  {'content': 'Cloning repository: simple-todo-list-90358-90367 (Container ID: 90367)...', 'sender': 'AI', 'timestamp': '2025-08-11T09:39:18.840606+00:00', 'user_id': 'admin', 'id': 'ffeb784b-670c-4c73-9372-cebb342ed013', 'status': 'RESOLVED', 'msg_type': 'SYSTEM'}
[2025-08-11 09:39:19] Sending message:  {'content': 'Successfully initialized workspace for simple-todo-list-90358-90367 at: /home/<USER>/workspace/code-generation/simple-todo-list-90358-90367', 'sender': 'AI', 'timestamp': '2025-08-11T09:39:18.844767+00:00', 'user_id': 'admin', 'id': 'f4673485-e900-4dde-99b4-ec174e08d8a9', 'status': 'RESOLVED', 'msg_type': 'SYSTEM'}
[2025-08-11 09:39:19] Sending message:  {'content': 'Setting Base path /home/<USER>/workspace/code-generation...', 'sender': 'AI', 'timestamp': '2025-08-11T09:39:18.856090+00:00', 'user_id': 'admin', 'id': 'ea83e4a8-b9d9-442d-b0a7-0549b348a88b', 'status': 'RESOLVED', 'msg_type': 'SYSTEM'}
[2025-08-11 09:39:19] 2025-08-11T09:39:18.937516+00:00 Added repository to Git safe directories: /home/<USER>/workspace/code-generation/simple-todo-list-90358-90367
[2025-08-11 09:39:19] 2025-08-11T09:39:18.941378+00:00 Set core.filemode to false for repository: /home/<USER>/workspace/code-generation/simple-todo-list-90358-90367
[2025-08-11 09:39:19] 2025-08-11T09:39:18.949048+00:00 ### SESSION MANAGEMENT ###
[2025-08-11 09:39:19] 2025-08-11T09:39:18.949101+00:00 ### Creating session branches
[2025-08-11 09:39:19] Event 'llm.timeoutevent' reported successfully
[2025-08-11 09:39:19] Event 'llm.timeout.count' reported successfully
[2025-08-11 09:39:19] 2025-08-11T09:39:19.357403+00:00 Fetched latest remote branches
[2025-08-11 09:39:19] 2025-08-11T09:39:19.385174+00:00 Found remote branches: ['kavia-main', 'main']
[2025-08-11 09:39:19] 2025-08-11T09:39:19.385204+00:00 kavia-main branch does not exist locally, creating it...
[2025-08-11 09:39:19] 2025-08-11T09:39:19.385214+00:00 kavia-main exists remotely, creating tracking branch...
[2025-08-11 09:39:19] 2025-08-11T09:39:19.390630+00:00 Stashed untracked files
[2025-08-11 09:39:19] 2025-08-11T09:39:19.395764+00:00 Error tracking remote kavia-main: Cmd('git') failed due to: exit code(128)
[2025-08-11 09:39:19]   cmdline: git checkout -b kavia-main --track origin/kavia-main
[2025-08-11 09:39:19]   stderr: 'fatal: A branch named 'kavia-main' already exists.'
[2025-08-11 09:39:19] 2025-08-11T09:39:19.395986+00:00 Falling back to creating from current branch: kavia-main
[2025-08-11 09:39:19] 2025-08-11T09:39:19.400094+00:00 Error: Could not create kavia-main branch: Cmd('git') failed due to: exit code(128)
[2025-08-11 09:39:19]   cmdline: git checkout -b kavia-main
[2025-08-11 09:39:19]   stderr: 'fatal: A branch named 'kavia-main' already exists.'
[2025-08-11 09:39:19] 2025-08-11T09:39:19.405557+00:00 Created and switched to new branch: cga-cgb11bdb92 from kavia-main
[2025-08-11 09:39:19] Sending message:  {'content': 'Workspace environment is ready to use', 'sender': 'AI', 'timestamp': '2025-08-11T09:39:19.681510+00:00', 'user_id': 'admin', 'id': 'b801af70-6fc5-4b22-a00c-2d09528348cd', 'status': 'RESOLVED', 'msg_type': 'SYSTEM'}
[2025-08-11 09:39:19] 2025-08-11T09:39:19.685055+00:00 DEBUG - Final repository_name: simple-todo-list-90358-90367
[2025-08-11 09:39:19] 2025-08-11T09:39:19.685077+00:00 DEBUG - Available keys in repository_metadata: ['service', 'repositoryName', 'repositoryId', 'cloneUrlHttp', 'cloneUrlSsh', 'organization', 'access_token_path', 'repositoryStatus', 'access_token', 'container_id', 'container_name']
[2025-08-11 09:39:19] 2025-08-11T09:39:19.685402+00:00 TaskExecutionAgent does not have request_context attribute, skipping design knowledge setup
[2025-08-11 09:39:19] Ensuring queue directory exists: /home/<USER>/workspace/.queue
[2025-08-11 09:39:19] Starting connection attempt...
[2025-08-11 09:39:19] Already connected, returning
[2025-08-11 09:39:19] Initializing filewatch for /home/<USER>/workspace/code-generation/.project_manifest.yaml
[2025-08-11 09:39:19] base_path: /home/<USER>/workspace/code-generation
[2025-08-11 09:39:19] ************************************syncup the temp-attacuments folder******************
[2025-08-11 09:39:19] kavia-attachments-dev-rdk7542
[2025-08-11 09:39:19] kavia-attachments-dev-rdk7542
[2025-08-11 09:39:19] Ensuring queue directory exists: /home/<USER>/workspace/.queue
[2025-08-11 09:39:19] 2025-08-11T09:39:19.698599+00:00 Inside the set Deployment controller
[2025-08-11 09:39:19] 2025-08-11T09:39:19.778189+00:00 Inside the set_container_deployment_controller 
[2025-08-11 09:39:19] 2025-08-11T09:39:19.778230+00:00 Inside the set preview controller
[2025-08-11 09:39:19] self.projectId 123
[2025-08-11 09:39:19] Starting connection attempt...
[2025-08-11 09:39:19] Already connected, returning
[2025-08-11 09:39:19] 2025-08-11T09:39:19.787220+00:00 previous_context {'_id': 'cgb11bdb92'}
[2025-08-11 09:39:19] 2025-08-11T09:39:19.787244+00:00 previous_context {}
WARNING:browsergym.core.env:Overriding the task's viewport parameter ({'width': 1280, 'height': 720} => {'width': 1200, 'height': 1600}). This might change the task's behaviour and difficulty.
INFO:botocore.credentials:Found credentials in environment variables.
INFO:app.classes.S3Handler:Successfully initialized S3Handler with buckets: kavia-attachments-dev-rdk7542 and kavia-profile-pictures-dev
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/code_generation_core_agent/agents/tools/web_browser.py", line 131, in run
    obs, info = globalBrowserEnvironment.reset()
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/browsergym/core/env.py", line 232, in reset
    self.browser = pw.chromium.launch(
                   ^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/playwright/sync_api/_generated.py", line 13991, in launch
    self._sync(
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_sync_base.py", line 115, in _sync
    return task.result()
           ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/asyncio/futures.py", line 203, in result
    raise self._exception.with_traceback(self._exception_tb)
  File "/usr/local/lib/python3.11/asyncio/tasks.py", line 277, in __step
    result = coro.send(None)
             ^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 94, in launch
    Browser, from_channel(await self._channel.send("launch", params))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 59, in send
    return await self._connection.wrap_api_call(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 514, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
playwright._impl._errors.Error: BrowserType.launch: 
╔══════════════════════════════════════════════════════╗
║ Host system is missing dependencies to run browsers. ║
║ Please install them with the following command:      ║
║                                                      ║
║     playwright install-deps                          ║
║                                                      ║
║ Alternatively, use apt:                              ║
║     apt-get install libatk1.0-0\                     ║
║         libatk-bridge2.0-0\                          ║
║         libcups2\                                    ║
║         libatspi2.0-0\                               ║
║         libxdamage1\                                 ║
║         libgbm1\                                     ║
║         libxkbcommon0\                               ║
║         libpango-1.0-0\                              ║
║         libcairo2\                                   ║
║         libasound2                                   ║
║                                                      ║
║ <3 Playwright Team                                   ║
╚══════════════════════════════════════════════════════╝
[92m09:39:27 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m09:39:28 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m09:39:28 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m09:39:28 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m09:39:29 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m09:39:29 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m09:39:29 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m09:39:29 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m09:39:30 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[2025-08-11 09:39:30] 2025-08-11T09:39:19.787255+00:00 Work_ITem_details: Type: <class 'dict'>, details: {'project_type': 'multi_container', 'project_name': 'todo_app', 'description': 'A simple todo application allowing users to add, view, edit, and delete their tasks.', 'env': {}, 'containers': [{'container_name': 'todo_frontend', 'platform': 'web', 'framework': 'react', 'description': 'Provides the user interface for managing todo items.', 'interfaces': 'User interface for creating, viewing, editing, and deleting todos', 'workspace': 'simple-todo-list-90358-90367', 'container_root': 'simple-todo-list-90358-90367/todo_frontend', 'dependencies': ['todo_database'], 'container_type': 'frontend', 'container_details': {'features': ['Add todo item', 'View todo list', 'Edit todo item', 'Delete todo item', 'Mark item as completed'], 'colors': {'primary': '#1e90ff', 'secondary': '#ff9800', 'accent': '#4caf50'}, 'theme': 'light', 'layout_description': 'A central list displaying todos with input at the top or modal for adding/editing, and buttons for actions next to each item.', 'style': 'minimalistic'}}], 'overview': {'project_name': 'todo_app', 'description': 'A simple todo application allowing users to add, view, edit, and delete their tasks.', 'env': {}, 'frontend_framework': 'react'}, '3rd_party_services': [], 'figma_components': '', 'manifest_path': '/home/<USER>/workspace/code-generation/.project_manifest.yaml'} 
[2025-08-11 09:39:30] 2025-08-11T09:39:19.790537+00:00  No Supabase credentials found for project None
[2025-08-11 09:39:30] 2025-08-11T09:39:19.790560+00:00 AGENT :  CodeGeneration
[2025-08-11 09:39:30] 2025-08-11T09:39:19.790569+00:00 PHASE TASK EXECUTION, START TIME:  2025-08-11T09:39:19.790574+00:00
[2025-08-11 09:39:30] 2025-08-11T09:39:19.790695+00:00 💰 Getting budget for user: 14d8e428-5001-701f-dee2-9f12f5ce8c56, tenant: rdk7542
[2025-08-11 09:39:30] 2025-08-11T09:39:19.790708+00:00 🏢 Is B2C tenant: False
[2025-08-11 09:39:30] 2025-08-11T09:39:19.790717+00:00 ⚠️ Not a B2C tenant, returning unlimited budget
[2025-08-11 09:39:30] 2025-08-11T09:39:19.790749+00:00 ===============================BUDGET=========================== inf
[2025-08-11 09:39:30] Starting progress callback in thread _progress_callback_thread_daemon_140635126282048
[2025-08-11 09:39:30] 2025-08-11T09:39:19.824591+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:19.824585+00:00
[2025-08-11 09:39:30] PROGRESSCALLBACK {'status': 'Starting task execution process for CodeGeneration...', 'total_tasks': '', 'latest_result': '', 'step': None}
[2025-08-11 09:39:30] <app.core.code_generation.Controller object at 0x7fe7ec75e0d0> <app.core.git_controller.GitController object at 0x7fe7ec763a10>
[2025-08-11 09:39:30] Progress update sent to WebSocket client
[2025-08-11 09:39:30] Controller is set
[2025-08-11 09:39:30] Progress callback thread _progress_callback_thread_daemon_140635126282048 completed
[2025-08-11 09:39:30] ---- INITIALISING SupabaseTool ---- with BASE_PATH: /home/<USER>/workspace/code-generation
[2025-08-11 09:39:30] ZK (<code_generation_core_agent.agents.preview.preview_manager.PreviewManager object at 0x7fe7f5e7a010>)Registering container: todo_frontend of type web at /home/<USER>/workspace/code-generation/simple-todo-list-90358-90367
[2025-08-11 09:39:30] Successfully saved manifest to /home/<USER>/workspace/code-generation/.project_manifest.yaml
[2025-08-11 09:39:30] Universal init status callback: todo_frontend, starting, Starting universal initialization for todo_frontend, None
[2025-08-11 09:39:30] 2025-08-11T09:39:20.443420+00:00 Successfully pushed branch cga-cgb11bdb92 to origin with upstream tracking
[2025-08-11 09:39:30] 2025-08-11T09:39:20.443445+00:00 ### Getting final current branch
[2025-08-11 09:39:30] 2025-08-11T09:39:20.443636+00:00 Branch operations completed: {'kavia_main_status': 'failed_fallback_to_kavia-main', 'task_branch_status': 'created_from_kavia_main_and_pushed', 'current_branch': 'cga-cgb11bdb92', 'gitignore_update': '', 'errors': ["Track remote kavia-main failed: Cmd('git') failed due to: exit code(128)\n  cmdline: git checkout -b kavia-main --track origin/kavia-main\n  stderr: 'fatal: A branch named 'kavia-main' already exists.'", "Create kavia-main failed: Cmd('git') failed due to: exit code(128)\n  cmdline: git checkout -b kavia-main\n  stderr: 'fatal: A branch named 'kavia-main' already exists.'"]}
[2025-08-11 09:39:30] 2025-08-11T09:39:20.443698+00:00 Warning: Could not set permissions: cannot access local variable 'subprocess' where it is not associated with a value
[2025-08-11 09:39:30] 🔒 Set 777 permissions for repository: /home/<USER>/workspace/code-generation/simple-todo-list-90358-90367
[2025-08-11 09:39:30] 2025-08-11T09:39:21.555642+00:00 ✅ Background clone completed for repository: simple-todo-list-90358-90367 at /home/<USER>/workspace/code-generation/simple-todo-list-90358-90367
[2025-08-11 09:39:30] 2025-08-11T09:39:21.555905+00:00 🔓 Removed PID 476 from filesystem lock for simple-todo-list-90358-90367
[2025-08-11 09:39:30] 2025-08-11T09:39:21.556028+00:00 🗑️ Deleted empty filesystem lock file
[2025-08-11 09:39:30] Universal init status callback: todo_frontend, success, Universal initialization completed successfully for todo_frontend, None
[2025-08-11 09:39:30] Universal init status callback: overall, completed, Universal initialization completed: 1/1 containers initialized successfully, None
[2025-08-11 09:39:30] UNIVERSAL INIT COMPLETED, TRIGGERING COMMIT CHANGES
[2025-08-11 09:39:30] Starting commit changes in thread _commit_changes_thread_daemon_140635126282048
[2025-08-11 09:39:30] Starting commit changes in GitController for task cgb11bdb92
[2025-08-11 09:39:30] Processing repository: /home/<USER>/workspace/code-generation/simple-todo-list-90358-90367
[2025-08-11 09:39:30] Commit changes triggered: <Thread(_commit_changes_thread_daemon_140635126282048, started daemon 140633805157952)>
[2025-08-11 09:39:30] ZK (<code_generation_core_agent.agents.preview.preview_manager.PreviewManager object at 0x7fe7f5e7a010>)Registering container: todo_frontend of type web at /home/<USER>/workspace/code-generation/simple-todo-list-90358-90367
[2025-08-11 09:39:30] Target branch: cga-cgb11bdb92
[2025-08-11 09:39:30] Project type ContainerType.FRONTEND
[2025-08-11 09:39:30] Checking workspace /home/<USER>/workspace/code-generation/simple-todo-list-90358-90367
[2025-08-11 09:39:30] Successfully saved manifest to /home/<USER>/workspace/code-generation/.project_manifest.yaml
[2025-08-11 09:39:30] Current branch: cga-cgb11bdb92
[2025-08-11 09:39:30] Added .file_activity_report.json to .gitignore
[2025-08-11 09:39:30] Unstaged .file_activity_report.json from git
[2025-08-11 09:39:30] ✅ File exclusions configured
[2025-08-11 09:39:30] Git status: On branch cga-cgb11bdb92
[2025-08-11 09:39:30] Your branch is up to date with 'origin/cga-cgb11bdb92'.
[2025-08-11 09:39:30] 
[2025-08-11 09:39:30] Changes not staged for commit:
[2025-08-11 09:39:30]   (use "git add <file>..." to update what will be committed)
[2025-08-11 09:39:30]   (use "git restore <file>..." to discard changes in working directory)
[2025-08-11 09:39:30]   modified:   .gitignore
[2025-08-11 09:39:30] 
[2025-08-11 09:39:30] Untracked files:
[2025-08-11 09:39:30]   (use "git add <file>..." to include in what will be committed)
[2025-08-11 09:39:30]   .init/
[2025-08-11 09:39:30]   .knowledge/
[2025-08-11 09:39:30]   todo_frontend/
[2025-08-11 09:39:30] 
[2025-08-11 09:39:30] no changes added to commit (use "git add" and/or "git commit -a")
[2025-08-11 09:39:30] Adding all files to git staging area...
[2025-08-11 09:39:30] Git add successful: 
[2025-08-11 09:39:30] Committing with message: CheckPoint - cgb11bdb92
[2025-08-11 09:39:30] Commit result: Committed changes with message: CheckPoint - cgb11bdb92
[2025-08-11 09:39:30] ✅ Commit successful
[2025-08-11 09:39:30] Push result: Successfully pushed changes to remote.
[2025-08-11 09:39:30] Git logs retrieved: 2
[2025-08-11 09:39:30] ✅ Checkpoint is created: 453f87d4
[2025-08-11 09:39:30] ✅ Created new checkpoint message: Rollback 1
[2025-08-11 09:39:30] 🏁 Commit changes completed for task cgb11bdb92
[2025-08-11 09:39:30] ✅ Commit changes completed: 1 checkpoints created
[2025-08-11 09:39:30] 🏁 Commit changes thread _commit_changes_thread_daemon_140635126282048 completed
[2025-08-11 09:39:30] Starting cost update in thread Thread-17 (_worker_loop)
[2025-08-11 09:39:30] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0001137}, Total cost: 0.0001137
[2025-08-11 09:39:30] [DEBUG] Starting session tracking for task_id: cgb11bdb92
[2025-08-11 09:39:30] [DEBUG] SessionTrackerService initialized with db: develop_kaviaroot
[2025-08-11 09:39:30] [DEBUG] Updating session cost for task_id: cgb11bdb92
[2025-08-11 09:39:30] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0001137}
[2025-08-11 09:39:30] [DEBUG] Total cost: 0.0001137
[2025-08-11 09:39:30] [DEBUG] Now updating costs - REPLACING current values
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m09:39:30 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m09:39:30 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[2025-08-11 09:39:30] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 9, 39, 30, 171378), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0001137}, 'total_cost': 0.0001137}
[2025-08-11 09:39:30] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 09:39:30] [SUCCESS] Session cost REPLACED for task_id: cgb11bdb92
[2025-08-11 09:39:30] [SUCCESS] New total_cost: 0.0001137
[2025-08-11 09:39:30] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0001137}
[2025-08-11 09:39:30] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 09:39:30] [SUCCESS] Session metadata updated for task_id: cgb11bdb92
[2025-08-11 09:39:30] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 09:39:30] [SUCCESS] Session cost tracking updated for task_id: cgb11bdb92
[2025-08-11 09:39:30] [SUCCESS] Costs REPLACED - Total: 0.0001137, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0001137}
[2025-08-11 09:39:30] 2025-08-11T09:39:30.225680+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:30.225669+00:00
[2025-08-11 09:39:30] 
[2025-08-11 09:39:30] 🔍 Starting B2B Cost Tracking
[2025-08-11 09:39:30] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:30] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:30] 💰 Total Cost: $0.000114
[2025-08-11 09:39:30] 🆔 Task ID: cgb11bdb92
[2025-08-11 09:39:30] 🏗️ Project ID: 90358
[2025-08-11 09:39:30] 🆕 Is New Task (Parameter): True
[2025-08-11 09:39:30] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0001137}
[2025-08-11 09:39:30] Starting cost update in thread Thread-20 (_worker_loop)
[2025-08-11 09:39:30] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0001773}, Total cost: 0.0001773
[2025-08-11 09:39:30] [DEBUG] Starting session tracking for task_id: cgb11bdb92
[2025-08-11 09:39:30] [DEBUG] Updating session cost for task_id: cgb11bdb92
[2025-08-11 09:39:30] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0001773}
[2025-08-11 09:39:30] [DEBUG] Total cost: 0.0001773
[2025-08-11 09:39:30] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 09:39:30] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 9, 39, 30, 258745), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0001773}, 'total_cost': 0.0001773}
[2025-08-11 09:39:30] 
[2025-08-11 09:39:30] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 09:39:30]    Total Users: 13
[2025-08-11 09:39:30]    Organization Cost: $886.105451
[2025-08-11 09:39:30] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:30] 🆕 Creating new project entry for Project ID: 90358
[2025-08-11 09:39:30] 
[2025-08-11 09:39:30] ================================================================================
[2025-08-11 09:39:30] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 09:39:30] ================================================================================
[2025-08-11 09:39:30] 📋 Task ID: cgb11bdb92
[2025-08-11 09:39:30] 🏗️ Project ID: 90358
[2025-08-11 09:39:30] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:30] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:30] 🆕 Is New Task (Parameter): True
[2025-08-11 09:39:30] 🔍 Task Exists in Tracking: False
[2025-08-11 09:39:30] ✅ Actually New Task: True
[2025-08-11 09:39:30] --------------------------------------------------------------------------------
[2025-08-11 09:39:30] 💸 COST BREAKDOWN:
[2025-08-11 09:39:30]    Current Task Cost: $0.000114
[2025-08-11 09:39:30]    Previous Task Cost: $0.000000
[2025-08-11 09:39:30]    Operation: APPENDED
[2025-08-11 09:39:30]    Reason: New task ID - appending cost to existing user total
[2025-08-11 09:39:30] --------------------------------------------------------------------------------
[2025-08-11 09:39:30] 📊 USER COST CHANGES:
[2025-08-11 09:39:30]    Original User Cost: $402.967618
[2025-08-11 09:39:30]    New User Cost: $402.967732
[2025-08-11 09:39:30]    User Cost Delta: $+0.000114
[2025-08-11 09:39:30]    📈 APPEND LOGIC: $402.967618 + $0.000114 = $402.967732
[2025-08-11 09:39:30] --------------------------------------------------------------------------------
[2025-08-11 09:39:30] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 09:39:30] [SUCCESS] Session cost REPLACED for task_id: cgb11bdb92
[2025-08-11 09:39:30] [SUCCESS] New total_cost: 0.0001773
[2025-08-11 09:39:30] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0001773}
[2025-08-11 09:39:30] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 09:39:30] [SUCCESS] Session metadata updated for task_id: cgb11bdb92
[2025-08-11 09:39:30] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 09:39:30] [SUCCESS] Session cost tracking updated for task_id: cgb11bdb92
[2025-08-11 09:39:30] [SUCCESS] Costs REPLACED - Total: 0.0001773, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0001773}
[2025-08-11 09:39:30] Starting cost update in thread Thread-16 (_worker_loop)
[2025-08-11 09:39:30] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0003058}, Total cost: 0.0003058
[2025-08-11 09:39:30] [DEBUG] Starting session tracking for task_id: cgb11bdb92
[2025-08-11 09:39:30] [DEBUG] Updating session cost for task_id: cgb11bdb92
[2025-08-11 09:39:30] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0003058}
[2025-08-11 09:39:30] [DEBUG] Total cost: 0.0003058
[2025-08-11 09:39:30] [DEBUG] Now updating costs - REPLACING current values
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m09:39:30 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[2025-08-11 09:39:30] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 9, 39, 30, 453646), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0003058}, 'total_cost': 0.0003058}
[2025-08-11 09:39:30] 2025-08-11T09:39:30.458738+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:30.458730+00:00
[2025-08-11 09:39:30] 
[2025-08-11 09:39:30] 🔍 Starting B2B Cost Tracking
[2025-08-11 09:39:30] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:30] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:30] 💰 Total Cost: $0.000177
[2025-08-11 09:39:30] 🆔 Task ID: cgb11bdb92
[2025-08-11 09:39:30] 🏗️ Project ID: 90358
[2025-08-11 09:39:30] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:30] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0001773}
[2025-08-11 09:39:30] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 09:39:30] [SUCCESS] Session cost REPLACED for task_id: cgb11bdb92
[2025-08-11 09:39:30] [SUCCESS] New total_cost: 0.0003058
[2025-08-11 09:39:30] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0003058}
[2025-08-11 09:39:30] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 09:39:30] 💾 LLM Costs Document Updated Successfully
[2025-08-11 09:39:30] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 09:39:30]    LLM Costs - User Cost: $402.967732
[2025-08-11 09:39:30]    LLM Costs - Organization Cost: $886.105565
[2025-08-11 09:39:30] --------------------------------------------------------------------------------
[2025-08-11 09:39:30] 🏢 ORGANIZATION COSTS:
[2025-08-11 09:39:30]    Original Org Cost: $886.105451
[2025-08-11 09:39:30]    New Org Cost: $886.105565
[2025-08-11 09:39:30]    Org Cost Delta: $+0.000114
[2025-08-11 09:39:30] --------------------------------------------------------------------------------
[2025-08-11 09:39:30] 📊 B2B COST UPDATE STATUS:
[2025-08-11 09:39:30]    LLM Costs Updated: ✅ YES
[2025-08-11 09:39:30]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 09:39:30] ================================================================================
[2025-08-11 09:39:30] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 09:39:30] 
[2025-08-11 09:39:30] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 09:39:30]    Total Users: 13
[2025-08-11 09:39:30]    Organization Cost: $886.105451
[2025-08-11 09:39:30] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:30] 🆕 Creating new project entry for Project ID: 90358
[2025-08-11 09:39:30] 
[2025-08-11 09:39:30] ================================================================================
[2025-08-11 09:39:30] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 09:39:30] ================================================================================
[2025-08-11 09:39:30] 📋 Task ID: cgb11bdb92
[2025-08-11 09:39:30] 🏗️ Project ID: 90358
[2025-08-11 09:39:30] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:30] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:30] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:30] 🔍 Task Exists in Tracking: False
[2025-08-11 09:39:30] ✅ Actually New Task: True
[2025-08-11 09:39:30] --------------------------------------------------------------------------------
[2025-08-11 09:39:30] 💸 COST BREAKDOWN:
[2025-08-11 09:39:30]    Current Task Cost: $0.000177
[2025-08-11 09:39:30]    Previous Task Cost: $0.000000
[2025-08-11 09:39:30]    Operation: APPENDED
[2025-08-11 09:39:30]    Reason: New task ID - appending cost to existing user total
[2025-08-11 09:39:30] --------------------------------------------------------------------------------
[2025-08-11 09:39:30] 📊 USER COST CHANGES:
[2025-08-11 09:39:30]    Original User Cost: $402.967618
[2025-08-11 09:39:30]    New User Cost: $402.967795
[2025-08-11 09:39:30]    User Cost Delta: $+0.000177
[2025-08-11 09:39:30]    📈 APPEND LOGIC: $402.967618 + $0.000177 = $402.967795
[2025-08-11 09:39:30] --------------------------------------------------------------------------------
[2025-08-11 09:39:30] [SUCCESS] Session metadata updated for task_id: cgb11bdb92
[2025-08-11 09:39:30] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 09:39:30] [SUCCESS] Session cost tracking updated for task_id: cgb11bdb92
[2025-08-11 09:39:30] [SUCCESS] Costs REPLACED - Total: 0.0003058, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0003058}
[2025-08-11 09:39:30] Starting cost update in thread Thread-21 (_worker_loop)
[2025-08-11 09:39:30] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00038909999999999997}, Total cost: 0.00038909999999999997
[2025-08-11 09:39:30] [DEBUG] Starting session tracking for task_id: cgb11bdb92
[2025-08-11 09:39:30] [DEBUG] Updating session cost for task_id: cgb11bdb92
[2025-08-11 09:39:30] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00038909999999999997}
[2025-08-11 09:39:30] [DEBUG] Total cost: 0.00038909999999999997
[2025-08-11 09:39:30] 2025-08-11T09:39:30.671221+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:30.671213+00:00
[2025-08-11 09:39:30] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 09:39:30] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 9, 39, 30, 686595), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00038909999999999997}, 'total_cost': 0.00038909999999999997}
[2025-08-11 09:39:30] 
[2025-08-11 09:39:30] 🔍 Starting B2B Cost Tracking
[2025-08-11 09:39:30] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:30] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:30] 💰 Total Cost: $0.000306
[2025-08-11 09:39:30] 🆔 Task ID: cgb11bdb92
[2025-08-11 09:39:30] 🏗️ Project ID: 90358
[2025-08-11 09:39:30] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:30] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0003058}
[2025-08-11 09:39:30] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 09:39:30] [SUCCESS] Session cost REPLACED for task_id: cgb11bdb92
[2025-08-11 09:39:30] [SUCCESS] New total_cost: 0.00038909999999999997
[2025-08-11 09:39:30] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00038909999999999997}
[2025-08-11 09:39:30] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 09:39:30] 
[2025-08-11 09:39:30] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 09:39:30]    Total Users: 13
[2025-08-11 09:39:30]    Organization Cost: $886.105565
[2025-08-11 09:39:30] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:30] 🏗️ Existing project entry found for Project ID: 90358
[2025-08-11 09:39:30] 
[2025-08-11 09:39:30] ================================================================================
[2025-08-11 09:39:30] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 09:39:30] ================================================================================
[2025-08-11 09:39:30] 📋 Task ID: cgb11bdb92
[2025-08-11 09:39:30] 🏗️ Project ID: 90358
[2025-08-11 09:39:30] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:30] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:30] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:30] 🔍 Task Exists in Tracking: True
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m09:39:30 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m09:39:30 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[92m09:39:30 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[2025-08-11 09:39:31] ✅ Actually New Task: False
[2025-08-11 09:39:31] --------------------------------------------------------------------------------
[2025-08-11 09:39:31] 💸 COST BREAKDOWN:
[2025-08-11 09:39:31]    Current Task Cost: $0.000306
[2025-08-11 09:39:31]    Previous Task Cost: $0.000114
[2025-08-11 09:39:31]    Operation: REPLACED
[2025-08-11 09:39:31]    Reason: Continuing same task - replacing previous cost of $0.000114
[2025-08-11 09:39:31] --------------------------------------------------------------------------------
[2025-08-11 09:39:31] 📊 USER COST CHANGES:
[2025-08-11 09:39:31]    Original User Cost: $402.967732
[2025-08-11 09:39:31]    New User Cost: $402.967924
[2025-08-11 09:39:31]    User Cost Delta: $+0.000192
[2025-08-11 09:39:31]    🔄 REPLACE LOGIC: $402.967732 - $0.000114 + $0.000306 = $402.967924
[2025-08-11 09:39:31] --------------------------------------------------------------------------------
[2025-08-11 09:39:31] [SUCCESS] Session metadata updated for task_id: cgb11bdb92
[2025-08-11 09:39:31] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 09:39:31] [SUCCESS] Session cost tracking updated for task_id: cgb11bdb92
[2025-08-11 09:39:31] [SUCCESS] Costs REPLACED - Total: 0.00038909999999999997, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00038909999999999997}
[2025-08-11 09:39:31] 2025-08-11T09:39:30.886547+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:30.886541+00:00
[2025-08-11 09:39:31] 
[2025-08-11 09:39:31] 🔍 Starting B2B Cost Tracking
[2025-08-11 09:39:31] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:31] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:31] 💰 Total Cost: $0.000389
[2025-08-11 09:39:31] 🆔 Task ID: cgb11bdb92
[2025-08-11 09:39:31] 🏗️ Project ID: 90358
[2025-08-11 09:39:31] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:31] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.00038909999999999997}
[2025-08-11 09:39:31] Starting cost update in thread Thread-18 (_worker_loop)
[2025-08-11 09:39:31] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0005356}, Total cost: 0.0005356
[2025-08-11 09:39:31] [DEBUG] Starting session tracking for task_id: cgb11bdb92
[2025-08-11 09:39:31] [DEBUG] Updating session cost for task_id: cgb11bdb92
[2025-08-11 09:39:31] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0005356}
[2025-08-11 09:39:31] [DEBUG] Total cost: 0.0005356
[2025-08-11 09:39:31] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 09:39:31] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 9, 39, 30, 924401), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0005356}, 'total_cost': 0.0005356}
[2025-08-11 09:39:31] 
[2025-08-11 09:39:31] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 09:39:31]    Total Users: 13
[2025-08-11 09:39:31]    Organization Cost: $886.105628
[2025-08-11 09:39:31] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:31] 🏗️ Existing project entry found for Project ID: 90358
[2025-08-11 09:39:31] 
[2025-08-11 09:39:31] ================================================================================
[2025-08-11 09:39:31] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 09:39:31] ================================================================================
[2025-08-11 09:39:31] 📋 Task ID: cgb11bdb92
[2025-08-11 09:39:31] 🏗️ Project ID: 90358
[2025-08-11 09:39:31] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:31] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:31] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:31] 🔍 Task Exists in Tracking: True
[2025-08-11 09:39:31] ✅ Actually New Task: False
[2025-08-11 09:39:31] --------------------------------------------------------------------------------
[2025-08-11 09:39:31] 💸 COST BREAKDOWN:
[2025-08-11 09:39:31]    Current Task Cost: $0.000389
[2025-08-11 09:39:31]    Previous Task Cost: $0.000177
[2025-08-11 09:39:31]    Operation: REPLACED
[2025-08-11 09:39:31]    Reason: Continuing same task - replacing previous cost of $0.000177
[2025-08-11 09:39:31] --------------------------------------------------------------------------------
[2025-08-11 09:39:31] 📊 USER COST CHANGES:
[2025-08-11 09:39:31]    Original User Cost: $402.967795
[2025-08-11 09:39:31]    New User Cost: $402.968007
[2025-08-11 09:39:31]    User Cost Delta: $+0.000212
[2025-08-11 09:39:31]    🔄 REPLACE LOGIC: $402.967795 - $0.000177 + $0.000389 = $402.968007
[2025-08-11 09:39:31] --------------------------------------------------------------------------------
[2025-08-11 09:39:31] 💾 LLM Costs Document Updated Successfully
[2025-08-11 09:39:31] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 09:39:31]    LLM Costs - User Cost: $402.967924
[2025-08-11 09:39:31]    LLM Costs - Organization Cost: $886.105757
[2025-08-11 09:39:31] --------------------------------------------------------------------------------
[2025-08-11 09:39:31] 🏢 ORGANIZATION COSTS:
[2025-08-11 09:39:31]    Original Org Cost: $886.105565
[2025-08-11 09:39:31]    New Org Cost: $886.105757
[2025-08-11 09:39:31]    Org Cost Delta: $+0.000192
[2025-08-11 09:39:31] --------------------------------------------------------------------------------
[2025-08-11 09:39:31] 📊 B2B COST UPDATE STATUS:
[2025-08-11 09:39:31]    LLM Costs Updated: ✅ YES
[2025-08-11 09:39:31]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 09:39:31] ================================================================================
[2025-08-11 09:39:31] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 09:39:31] Starting cost update in thread Thread-15 (_worker_loop)
[2025-08-11 09:39:31] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0006152}, Total cost: 0.0006152
[2025-08-11 09:39:31] [DEBUG] Starting session tracking for task_id: cgb11bdb92
[2025-08-11 09:39:31] [DEBUG] Updating session cost for task_id: cgb11bdb92
[2025-08-11 09:39:31] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0006152}
[2025-08-11 09:39:31] [DEBUG] Total cost: 0.0006152
[2025-08-11 09:39:31] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 09:39:31] [SUCCESS] Session cost REPLACED for task_id: cgb11bdb92
[2025-08-11 09:39:31] [SUCCESS] New total_cost: 0.0005356
[2025-08-11 09:39:31] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0005356}
[2025-08-11 09:39:31] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 09:39:31] Starting cost update in thread Thread-19 (_worker_loop)
[2025-08-11 09:39:31] \00t\00\00\00\00\00\00\00\00\00\00\00\00\00\00`\9D\80\96\A1U\00\00.\00\00\00\00\00\00\FF\FF\FF\FF\FF\FF\FF\FF\E4\00\00\8C\E7\00\00\00\00\00\00\00\00\00\00model_info: {'key': 'gpt-4.1-nano-2025-04-14', 'max_tokens': 32768, 'max_input_tokens': 1047576, 'max_output_tokens': 32768, 'input_cost_per_token': 1e-07, 'cache_creation_input_token_cost': None, 'cache_read_input_token_cost': 2.5e-08, 'input_cost_per_character': None, 'input_cost_per_token_above_128k_tokens': None, 'input_cost_per_token_above_200k_tokens': None, 'input_cost_per_query': None, 'input_cost_per_second': None, 'input_cost_per_audio_token': None, 'input_cost_per_token_batches': 5e-08, 'output_cost_per_token_batches': 2e-07, 'output_cost_per_token': 4e-07, 'output_cost_per_audio_token': None, 'output_cost_per_character': NonAgent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0007002}, Total cost: 0.0007002
[2025-08-11 09:39:31] [DEBUG] Starting session tracking for task_id: cgb11bdb92
[2025-08-11 09:39:31] [DEBUG] Updating session cost for task_id: cgb11bdb92
[2025-08-11 09:39:31] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0007002}
[2025-08-11 09:39:31] [DEBUG] Total cost: 0.0007002
[2025-08-11 09:39:31] [SUCCESS] Session metadata updated for task_id: cgb11bdb92
[2025-08-11 09:39:31] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 09:39:31] [SUCCESS] Session cost tracking updated for task_id: cgb11bdb92
[2025-08-11 09:39:31] [SUCCESS] Costs REPLACED - Total: 0.0005356, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0005356}
[2025-08-11 09:39:31] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 09:39:31] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 9, 39, 31, 437813), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0006152}, 'total_cost': 0.0006152}
[2025-08-11 09:39:31] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 09:39:31] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 9, 39, 31, 440098), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0007002}, 'total_cost': 0.0007002}
[2025-08-11 09:39:31] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 09:39:31] [SUCCESS] Session cost REPLACED for task_id: cgb11bdb92
[2025-08-11 09:39:31] [SUCCESS] New total_cost: 0.0007002
[2025-08-11 09:39:31] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0007002}
[2025-08-11 09:39:31] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 09:39:31] 2025-08-11T09:39:31.454172+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:31.454164+00:00
[2025-08-11 09:39:31] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 09:39:31] [SUCCESS] Session cost REPLACED for task_id: cgb11bdb92
[2025-08-11 09:39:31] [SUCCESS] New total_cost: 0.0006152
[2025-08-11 09:39:31] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0006152}
[2025-08-11 09:39:31] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 09:39:31] [SUCCESS] Session metadata updated for task_id: cgb11bdb92
[2025-08-11 09:39:31] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 09:39:31] [SUCCESS] Session cost tracking updated for task_id: cgb11bdb92
[2025-08-11 09:39:31] [SUCCESS] Costs REPLACED - Total: 0.0007002, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0007002}
[2025-08-11 09:39:31] 
[2025-08-11 09:39:31] 🔍 Starting B2B Cost Tracking
[2025-08-11 09:39:31] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:31] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:31] 💰 Total Cost: $0.000536
[2025-08-11 09:39:31] 🆔 Task ID: cgb11bdb92
[2025-08-11 09:39:31] 🏗️ Project ID: 90358
[2025-08-11 09:39:31] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:31] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0005356}
[2025-08-11 09:39:31] 2025-08-11T09:39:31.466721+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:31.466715+00:00
[2025-08-11 09:39:31] 💾 LLM Costs Document Updated Successfully
[2025-08-11 09:39:31] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 09:39:31]    LLM Costs - User Cost: $402.968007
[2025-08-11 09:39:31]    LLM Costs - Organization Cost: $886.105840
[2025-08-11 09:39:31] --------------------------------------------------------------------------------
[2025-08-11 09:39:31] 🏢 ORGANIZATION COSTS:
[2025-08-11 09:39:31]    Original Org Cost: $886.105628
[2025-08-11 09:39:31]    New Org Cost: $886.105840
[2025-08-11 09:39:31]    Org Cost Delta: $+0.000212
[2025-08-11 09:39:31] --------------------------------------------------------------------------------
[2025-08-11 09:39:31] 📊 B2B COST UPDATE STATUS:
[2025-08-11 09:39:31]    LLM Costs Updated: ✅ YES
[2025-08-11 09:39:31]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 09:39:31] ================================================================================
[2025-08-11 09:39:31] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 09:39:31] 
[2025-08-11 09:39:31] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 09:39:31]    Total Users: 13
[2025-08-11 09:39:31]    Organization Cost: $886.105757
[2025-08-11 09:39:31] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:31] 🏗️ Existing project entry found for Project ID: 90358
[2025-08-11 09:39:31] 
[2025-08-11 09:39:31] ================================================================================
[2025-08-11 09:39:31] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 09:39:31] ================================================================================
[2025-08-11 09:39:31] 📋 Task ID: cgb11bdb92
[2025-08-11 09:39:31] 🏗️ Project ID: 90358
[2025-08-11 09:39:31] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:31] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:31] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:31] 🔍 Task Exists in Tracking: True
[2025-08-11 09:39:31] ✅ Actually New Task: False
[2025-08-11 09:39:31] --------------------------------------------------------------------------------
[2025-08-11 09:39:31] 💸 COST BREAKDOWN:
[2025-08-11 09:39:31]    Current Task Cost: $0.000536
[2025-08-11 09:39:31]    Previous Task Cost: $0.000306
[2025-08-11 09:39:31]    Operation: REPLACED
[2025-08-11 09:39:31]    Reason: Continuing same task - replacing previous cost of $0.000306
[2025-08-11 09:39:31] --------------------------------------------------------------------------------
[2025-08-11 09:39:31] 📊 USER COST CHANGES:
[2025-08-11 09:39:31]    Original User Cost: $402.967924
[2025-08-11 09:39:31]    New User Cost: $402.968154
[2025-08-11 09:39:31]    User Cost Delta: $+0.000230
[2025-08-11 09:39:31]    🔄 REPLACE LOGIC: $402.967924 - $0.000306 + $0.000536 = $402.968154
[2025-08-11 09:39:31] --------------------------------------------------------------------------------
[2025-08-11 09:39:31] [SUCCESS] Session metadata updated for task_id: cgb11bdb92
[2025-08-11 09:39:31] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 09:39:31] [SUCCESS] Session cost tracking updated for task_id: cgb11bdb92
[2025-08-11 09:39:31] [SUCCESS] Costs REPLACED - Total: 0.0006152, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0006152}
[2025-08-11 09:39:31] 2025-08-11T09:39:31.727543+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:31.727537+00:00
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m09:39:32 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[92m09:39:32 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m09:39:32 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[2025-08-11 09:39:32] 
[2025-08-11 09:39:32] 🔍 Starting B2B Cost Tracking
[2025-08-11 09:39:32] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:32] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:32] 💰 Total Cost: $0.000700
[2025-08-11 09:39:32] 🆔 Task ID: cgb11bdb92
[2025-08-11 09:39:32] 🏗️ Project ID: 90358
[2025-08-11 09:39:32] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:32] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0007002}
[2025-08-11 09:39:32] Cost update thread Thread-17 (_worker_loop) completed
[2025-08-11 09:39:32] 💾 LLM Costs Document Updated Successfully
[2025-08-11 09:39:32] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 09:39:32]    LLM Costs - User Cost: $402.968154
[2025-08-11 09:39:32]    LLM Costs - Organization Cost: $886.105987
[2025-08-11 09:39:32] --------------------------------------------------------------------------------
[2025-08-11 09:39:32] 🏢 ORGANIZATION COSTS:
[2025-08-11 09:39:32]    Original Org Cost: $886.105757
[2025-08-11 09:39:32]    New Org Cost: $886.105987
[2025-08-11 09:39:32]    Org Cost Delta: $+0.000230
[2025-08-11 09:39:32] --------------------------------------------------------------------------------
[2025-08-11 09:39:32] 📊 B2B COST UPDATE STATUS:
[2025-08-11 09:39:32]    LLM Costs Updated: ✅ YES
[2025-08-11 09:39:32]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 09:39:32] ================================================================================
[2025-08-11 09:39:32] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 09:39:32] 
[2025-08-11 09:39:32] 🔍 Starting B2B Cost Tracking
[2025-08-11 09:39:32] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:32] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:32] 💰 Total Cost: $0.000615
[2025-08-11 09:39:32] 🆔 Task ID: cgb11bdb92
[2025-08-11 09:39:32] 🏗️ Project ID: 90358
[2025-08-11 09:39:32] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:32] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0006152}
[2025-08-11 09:39:32] Cost update thread Thread-20 (_worker_loop) completed
[2025-08-11 09:39:32] 
[2025-08-11 09:39:32] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 09:39:32]    Total Users: 13
[2025-08-11 09:39:32]    Organization Cost: $886.105840
[2025-08-11 09:39:32] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:32] 🏗️ Existing project entry found for Project ID: 90358
[2025-08-11 09:39:32] 
[2025-08-11 09:39:32] ================================================================================
[2025-08-11 09:39:32] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 09:39:32] ================================================================================
[2025-08-11 09:39:32] 📋 Task ID: cgb11bdb92
[2025-08-11 09:39:32] 🏗️ Project ID: 90358
[2025-08-11 09:39:32] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:32] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:32] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:32] 🔍 Task Exists in Tracking: True
[2025-08-11 09:39:32] ✅ Actually New Task: False
[2025-08-11 09:39:32] --------------------------------------------------------------------------------
[2025-08-11 09:39:32] 💸 COST BREAKDOWN:
[2025-08-11 09:39:32]    Current Task Cost: $0.000700
[2025-08-11 09:39:32]    Previous Task Cost: $0.000389
[2025-08-11 09:39:32]    Operation: REPLACED
[2025-08-11 09:39:32]    Reason: Continuing same task - replacing previous cost of $0.000389
[2025-08-11 09:39:32] --------------------------------------------------------------------------------
[2025-08-11 09:39:32] 📊 USER COST CHANGES:
[2025-08-11 09:39:32]    Original User Cost: $402.968007
[2025-08-11 09:39:32]    New User Cost: $402.968318
[2025-08-11 09:39:32]    User Cost Delta: $+0.000311
[2025-08-11 09:39:32]    🔄 REPLACE LOGIC: $402.968007 - $0.000389 + $0.000700 = $402.968318
[2025-08-11 09:39:32] --------------------------------------------------------------------------------
[2025-08-11 09:39:32] 
[2025-08-11 09:39:32] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 09:39:32]    Total Users: 13
[2025-08-11 09:39:32]    Organization Cost: $886.105987
[2025-08-11 09:39:32] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:32] 🏗️ Existing project entry found for Project ID: 90358
[2025-08-11 09:39:32] 
[2025-08-11 09:39:32] ================================================================================
[2025-08-11 09:39:32] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 09:39:32] ================================================================================
[2025-08-11 09:39:32] 📋 Task ID: cgb11bdb92
[2025-08-11 09:39:32] 🏗️ Project ID: 90358
[2025-08-11 09:39:32] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:32] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:32] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:32] 🔍 Task Exists in Tracking: True
[2025-08-11 09:39:32] ✅ Actually New Task: False
[2025-08-11 09:39:32] --------------------------------------------------------------------------------
[2025-08-11 09:39:32] 💸 COST BREAKDOWN:
[2025-08-11 09:39:32]    Current Task Cost: $0.000615
[2025-08-11 09:39:32]    Previous Task Cost: $0.000536
[2025-08-11 09:39:32]    Operation: REPLACED
[2025-08-11 09:39:32]    Reason: Continuing same task - replacing previous cost of $0.000536
[2025-08-11 09:39:32] --------------------------------------------------------------------------------
[2025-08-11 09:39:32] 📊 USER COST CHANGES:
[2025-08-11 09:39:32]    Original User Cost: $402.968154
[2025-08-11 09:39:32]    New User Cost: $402.968233
[2025-08-11 09:39:32]    User Cost Delta: $+0.000079
[2025-08-11 09:39:32]    🔄 REPLACE LOGIC: $402.968154 - $0.000536 + $0.000615 = $402.968233
[2025-08-11 09:39:32] --------------------------------------------------------------------------------
[2025-08-11 09:39:32] 💾 LLM Costs Document Updated Successfully
[2025-08-11 09:39:32] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 09:39:32]    LLM Costs - User Cost: $402.968318
[2025-08-11 09:39:32]    LLM Costs - Organization Cost: $886.106151
[2025-08-11 09:39:32] --------------------------------------------------------------------------------
[2025-08-11 09:39:32] 🏢 ORGANIZATION COSTS:
[2025-08-11 09:39:32]    Original Org Cost: $886.105840
[2025-08-11 09:39:32]    New Org Cost: $886.106151
[2025-08-11 09:39:32]    Org Cost Delta: $+0.000311
[2025-08-11 09:39:32] --------------------------------------------------------------------------------
[2025-08-11 09:39:32] 📊 B2B COST UPDATE STATUS:
[2025-08-11 09:39:32]    LLM Costs Updated: ✅ YES
[2025-08-11 09:39:32]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 09:39:32] ================================================================================
[2025-08-11 09:39:32] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 09:39:32] 💾 LLM Costs Document Updated Successfully
[2025-08-11 09:39:32] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 09:39:32]    LLM Costs - User Cost: $402.968233
[2025-08-11 09:39:32]    LLM Costs - Organization Cost: $886.106066
[2025-08-11 09:39:32] --------------------------------------------------------------------------------
[2025-08-11 09:39:32] 🏢 ORGANIZATION COSTS:
[2025-08-11 09:39:32]    Original Org Cost: $886.105987
[2025-08-11 09:39:32]    New Org Cost: $886.106066
[2025-08-11 09:39:32]    Org Cost Delta: $+0.000079
[2025-08-11 09:39:32] --------------------------------------------------------------------------------
[2025-08-11 09:39:32] 📊 B2B COST UPDATE STATUS:
[2025-08-11 09:39:32]    LLM Costs Updated: ✅ YES
[2025-08-11 09:39:32]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 09:39:32] ================================================================================
[2025-08-11 09:39:32] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 09:39:32] Cost update thread Thread-21 (_worker_loop) completed
[2025-08-11 09:39:32] Metrics submitted successfully: 1 datapoints
[2025-08-11 09:39:32] Cost update thread Thread-16 (_worker_loop) completed
[2025-08-11 09:39:32] Starting task start callback in thread _task_start_callback_thread_daemon_140635126282048
[2025-08-11 09:39:32] NoneType: None
[2025-08-11 09:39:32] 
[2025-08-11 09:39:32] 2025-08-11T09:39:32.865044+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:32.865038+00:00
[2025-08-11 09:39:32] Message added ChatMessage(id=53604f77-790c-4958-9ee4-6eab8c379229, type = llm content= 
[2025-08-11 09:39:32] 
[2025-08-11 09:39:32]  # 🤖 Welcome to Kavia Code Assistant!
[2025-08-11 09:39:32] 
[2025-08-11 09:39:32] I'm here to help you develop a **simple Todo application** using React. This app will allow users to add, view, edit, delete, and mark tasks as completed, all with a minimalistic and light theme.
[2025-08-11 09:39:32] 
[2025-08-11 09:39:32] ## What would you like to do next?
[2025-08-11 09:39:32] 
[2025-08-11 09:39:32] • 💻 **Write all the code for this**
[2025-08-11 09:39:32] • 📝 **Create a plan**
[2025-08-11 09:39:32] • 🧪 **Build a test suite**
[2025-08-11 09:39:32] • ▶️ **Run the tests**
[2025-08-11 09:39:32] • 📃 **Create architecture documents**
[2025-08-11 09:39:32] • ❓ **Answer questions about the codebase / architecture**
[2025-08-11 09:39:32] • 🆘 **Help**
[2025-08-11 09:39:32] 
[2025-08-11 09:39:32] Let me know how you'd like to proceed!, status=MessageStatus.PENDING), parent_id=None), metadata=None), timestamp=2025-08-11 09:39:32.864752),requires_resolution=True), extra={})
[2025-08-11 09:39:32] PHASE FIRST MESSAGE, START TIME:  2025-08-11T09:39:32.865205+00:00
[2025-08-11 09:39:32] Message dict {'id': '53604f77-790c-4958-9ee4-6eab8c379229', 'content': " \n\n # 🤖 Welcome to Kavia Code Assistant!\n\nI'm here to help you develop a **simple Todo application** using React. This app will allow users to add, view, edit, delete, and mark tasks as completed, all with a minimalistic and light theme.\n\n## What would you like to do next?\n\n• 💻 **Write all the code for this**\n• 📝 **Create a plan**\n• 🧪 **Build a test suite**\n• ▶️ **Run the tests**\n• 📃 **Create architecture documents**\n• ❓ **Answer questions about the codebase / architecture**\n• 🆘 **Help**\n\nLet me know how you'd like to proceed!", 'msg_type': 'llm', 'timestamp': '2025-08-11T09:39:32.864752+00:00', 'status': 'pending', 'requires_resolution': True, 'resolution_id': None, 'parent_id': None, 'metadata': None, 'attachments': [], 'extra': {}}
[92m09:39:33 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:code_generation_core_agent.agents.framework.knowledge_embeddings:Lazily initializing Milvus client for todo_frontend
INFO:pymilvus.orm.connections:Pass in the local path /home/<USER>/workspace/code-generation/simple-todo-list-90358-90367/.knowledge/.vector_db/milvus.db, and run it using milvus-lite
DEBUG:pymilvus.milvus_client.milvus_client:Created new connection using: cf2e60857cbc49768b4b571d058bebc6
DEBUG:pymilvus.milvus_client.milvus_client:Successfully created collection: file_search_terms_embeddings
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m09:39:34 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
DEBUG:pymilvus.milvus_client.milvus_client:Successfully created an index on collection: file_search_terms_embeddings
INFO:code_generation_core_agent.agents.framework.knowledge_embeddings:Search terms embeddings file_search_terms_embeddings loaded: {'state': <LoadState: Loaded>}
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m09:39:35 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[2025-08-11 09:39:35] 2025-08-11T09:39:32.867895+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:32.867891+00:00
[2025-08-11 09:39:35] Message needs response ChatMessage(id=53604f77-790c-4958-9ee4-6eab8c379229, type = llm content= 
[2025-08-11 09:39:35] 
[2025-08-11 09:39:35]  # 🤖 Welcome to Kavia Code Assistant!
[2025-08-11 09:39:35] 
[2025-08-11 09:39:35] I'm here to help you develop a **simple Todo application** using React. This app will allow users to add, view, edit, delete, and mark tasks as completed, all with a minimalistic and light theme.
[2025-08-11 09:39:35] 
[2025-08-11 09:39:35] ## What would you like to do next?
[2025-08-11 09:39:35] 
[2025-08-11 09:39:35] • 💻 **Write all the code for this**
[2025-08-11 09:39:35] • 📝 **Create a plan**
[2025-08-11 09:39:35] • 🧪 **Build a test suite**
[2025-08-11 09:39:35] • ▶️ **Run the tests**
[2025-08-11 09:39:35] • 📃 **Create architecture documents**
[2025-08-11 09:39:35] • ❓ **Answer questions about the codebase / architecture**
[2025-08-11 09:39:35] • 🆘 **Help**
[2025-08-11 09:39:35] 
[2025-08-11 09:39:35] Let me know how you'd like to proceed!, status=MessageStatus.NEEDS_RESPONSE), parent_id=None), metadata=None), timestamp=2025-08-11 09:39:32.864752+00:00),requires_resolution=True), extra={})
[2025-08-11 09:39:35] Cost update thread Thread-18 (_worker_loop) completed
[2025-08-11 09:39:35] Task start callback thread _task_start_callback_thread_daemon_140635126282048 completed
[2025-08-11 09:39:35] Cost update thread Thread-19 (_worker_loop) completed
[2025-08-11 09:39:35] Cost update thread Thread-15 (_worker_loop) completed
[2025-08-11 09:39:35] Starting cost update in thread Thread-21 (_worker_loop)
[2025-08-11 09:39:35] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0007741, 'TaskExecutionAgent': 0.0005572999999999999}, Total cost: 0.0013314
[2025-08-11 09:39:35] [DEBUG] Starting session tracking for task_id: cgb11bdb92
[2025-08-11 09:39:35] [DEBUG] Updating session cost for task_id: cgb11bdb92
[2025-08-11 09:39:35] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0007741, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:35] [DEBUG] Total cost: 0.0013314
[2025-08-11 09:39:35] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 09:39:35] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 9, 39, 34, 564540), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0007741, 'TaskExecutionAgent': 0.0005572999999999999}, 'total_cost': 0.0013314}
[2025-08-11 09:39:35] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 09:39:35] [SUCCESS] Session cost REPLACED for task_id: cgb11bdb92
[2025-08-11 09:39:35] [SUCCESS] New total_cost: 0.0013314
[2025-08-11 09:39:35] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0007741, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:35] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 09:39:35] [SUCCESS] Session metadata updated for task_id: cgb11bdb92
[2025-08-11 09:39:35] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 09:39:35] [SUCCESS] Session cost tracking updated for task_id: cgb11bdb92
[2025-08-11 09:39:35] [SUCCESS] Costs REPLACED - Total: 0.0013314, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0007741, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:35] 2025-08-11T09:39:34.583600+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:34.583595+00:00
[2025-08-11 09:39:35] 
[2025-08-11 09:39:35] 🔍 Starting B2B Cost Tracking
[2025-08-11 09:39:35] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:35] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:35] 💰 Total Cost: $0.001331
[2025-08-11 09:39:35] 🆔 Task ID: cgb11bdb92
[2025-08-11 09:39:35] 🏗️ Project ID: 90358
[2025-08-11 09:39:35] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:35] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0007741, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:35] 
[2025-08-11 09:39:35] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 09:39:35]    Total Users: 13
[2025-08-11 09:39:35]    Organization Cost: $886.106066
[2025-08-11 09:39:35] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:35] 🏗️ Existing project entry found for Project ID: 90358
[2025-08-11 09:39:35] 
[2025-08-11 09:39:35] ================================================================================
[2025-08-11 09:39:35] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 09:39:35] ================================================================================
[2025-08-11 09:39:35] 📋 Task ID: cgb11bdb92
[2025-08-11 09:39:35] 🏗️ Project ID: 90358
[2025-08-11 09:39:35] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:35] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:35] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:35] 🔍 Task Exists in Tracking: True
[2025-08-11 09:39:35] ✅ Actually New Task: False
[2025-08-11 09:39:35] --------------------------------------------------------------------------------
[2025-08-11 09:39:35] 💸 COST BREAKDOWN:
[2025-08-11 09:39:35]    Current Task Cost: $0.001331
[2025-08-11 09:39:35]    Previous Task Cost: $0.000615
[2025-08-11 09:39:35]    Operation: REPLACED
[2025-08-11 09:39:35]    Reason: Continuing same task - replacing previous cost of $0.000615
[2025-08-11 09:39:35] --------------------------------------------------------------------------------
[2025-08-11 09:39:35] 📊 USER COST CHANGES:
[2025-08-11 09:39:35]    Original User Cost: $402.968233
[2025-08-11 09:39:35]    New User Cost: $402.968949
[2025-08-11 09:39:35]    User Cost Delta: $+0.000716
[2025-08-11 09:39:35]    🔄 REPLACE LOGIC: $402.968233 - $0.000615 + $0.001331 = $402.968949
[2025-08-11 09:39:35] --------------------------------------------------------------------------------
[2025-08-11 09:39:35] 💾 LLM Costs Document Updated Successfully
[2025-08-11 09:39:35] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 09:39:35]    LLM Costs - User Cost: $402.968949
[2025-08-11 09:39:35]    LLM Costs - Organization Cost: $886.106782
[2025-08-11 09:39:35] --------------------------------------------------------------------------------
[2025-08-11 09:39:35] 🏢 ORGANIZATION COSTS:
[2025-08-11 09:39:35]    Original Org Cost: $886.106066
[2025-08-11 09:39:35]    New Org Cost: $886.106782
[2025-08-11 09:39:35]    Org Cost Delta: $+0.000716
[2025-08-11 09:39:35] --------------------------------------------------------------------------------
[2025-08-11 09:39:35] 📊 B2B COST UPDATE STATUS:
[2025-08-11 09:39:35]    LLM Costs Updated: ✅ YES
[2025-08-11 09:39:35]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 09:39:35] ================================================================================
[2025-08-11 09:39:35] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 09:39:35] Cost update thread Thread-21 (_worker_loop) completed
[2025-08-11 09:39:35] Starting cost update in thread Thread-17 (_worker_loop)
[2025-08-11 09:39:35] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0009103, 'TaskExecutionAgent': 0.0005572999999999999}, Total cost: 0.0014676
[2025-08-11 09:39:35] [DEBUG] Starting session tracking for task_id: cgb11bdb92
[2025-08-11 09:39:35] [DEBUG] Updating session cost for task_id: cgb11bdb92
[2025-08-11 09:39:35] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0009103, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:35] [DEBUG] Total cost: 0.0014676
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m09:39:35 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
[2025-08-11 09:39:35] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 09:39:35] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 9, 39, 35, 24626), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0009103, 'TaskExecutionAgent': 0.0005572999999999999}, 'total_cost': 0.0014676}
[2025-08-11 09:39:35] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 09:39:35] [SUCCESS] Session cost REPLACED for task_id: cgb11bdb92
[2025-08-11 09:39:35] [SUCCESS] New total_cost: 0.0014676
[2025-08-11 09:39:35] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0009103, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:35] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 09:39:35] [SUCCESS] Session metadata updated for task_id: cgb11bdb92
[2025-08-11 09:39:35] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 09:39:35] [SUCCESS] Session cost tracking updated for task_id: cgb11bdb92
[2025-08-11 09:39:35] [SUCCESS] Costs REPLACED - Total: 0.0014676, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0009103, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:35] 2025-08-11T09:39:35.056720+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:35.056714+00:00
[2025-08-11 09:39:35] 
[2025-08-11 09:39:35] 🔍 Starting B2B Cost Tracking
[2025-08-11 09:39:35] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:35] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:35] 💰 Total Cost: $0.001468
[2025-08-11 09:39:35] 🆔 Task ID: cgb11bdb92
[2025-08-11 09:39:35] 🏗️ Project ID: 90358
[2025-08-11 09:39:35] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:35] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0009103, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:35] 
[2025-08-11 09:39:35] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 09:39:35]    Total Users: 13
[2025-08-11 09:39:35]    Organization Cost: $886.106782
[2025-08-11 09:39:35] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:35] 🏗️ Existing project entry found for Project ID: 90358
[2025-08-11 09:39:35] 
[2025-08-11 09:39:35] ================================================================================
[2025-08-11 09:39:35] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 09:39:35] ================================================================================
[2025-08-11 09:39:35] 📋 Task ID: cgb11bdb92
[2025-08-11 09:39:35] 🏗️ Project ID: 90358
[2025-08-11 09:39:35] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:35] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:35] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:35] 🔍 Task Exists in Tracking: True
[2025-08-11 09:39:35] ✅ Actually New Task: False
[2025-08-11 09:39:35] --------------------------------------------------------------------------------
[2025-08-11 09:39:35] 💸 COST BREAKDOWN:
[2025-08-11 09:39:35]    Current Task Cost: $0.001468
[2025-08-11 09:39:35]    Previous Task Cost: $0.001331
[2025-08-11 09:39:35]    Operation: REPLACED
[2025-08-11 09:39:35]    Reason: Continuing same task - replacing previous cost of $0.001331
[2025-08-11 09:39:35] --------------------------------------------------------------------------------
[2025-08-11 09:39:35] 📊 USER COST CHANGES:
[2025-08-11 09:39:35]    Original User Cost: $402.968949
[2025-08-11 09:39:35]    New User Cost: $402.969086
[2025-08-11 09:39:35]    User Cost Delta: $+0.000137
[2025-08-11 09:39:35]    🔄 REPLACE LOGIC: $402.968949 - $0.001331 + $0.001468 = $402.969086
[2025-08-11 09:39:35] --------------------------------------------------------------------------------
[2025-08-11 09:39:35] 💾 LLM Costs Document Updated Successfully
[2025-08-11 09:39:35] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 09:39:35]    LLM Costs - User Cost: $402.969086
[2025-08-11 09:39:35]    LLM Costs - Organization Cost: $886.106919
[2025-08-11 09:39:35] --------------------------------------------------------------------------------
[2025-08-11 09:39:35] 🏢 ORGANIZATION COSTS:
[2025-08-11 09:39:35]    Original Org Cost: $886.106782
[2025-08-11 09:39:35]    New Org Cost: $886.106919
[2025-08-11 09:39:35]    Org Cost Delta: $+0.000137
[2025-08-11 09:39:35] --------------------------------------------------------------------------------
[2025-08-11 09:39:35] 📊 B2B COST UPDATE STATUS:
[2025-08-11 09:39:35]    LLM Costs Updated: ✅ YES
[2025-08-11 09:39:35]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 09:39:35] ================================================================================
[2025-08-11 09:39:35] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 09:39:35] Starting cost update in thread Thread-20 (_worker_loop)
[2025-08-11 09:39:35] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0010501999999999998, 'TaskExecutionAgent': 0.0005572999999999999}, Total cost: 0.0016074999999999998
[2025-08-11 09:39:35] [DEBUG] Starting session tracking for task_id: cgb11bdb92
[2025-08-11 09:39:35] [DEBUG] Updating session cost for task_id: cgb11bdb92
[2025-08-11 09:39:35] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0010501999999999998, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:35] [DEBUG] Total cost: 0.0016074999999999998
[2025-08-11 09:39:35] Cost update thread Thread-17 (_worker_loop) completed
[2025-08-11 09:39:35] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 09:39:35] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 9, 39, 35, 667697), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0010501999999999998, 'TaskExecutionAgent': 0.0005572999999999999}, 'total_cost': 0.0016074999999999998}
[2025-08-11 09:39:35] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 09:39:35] [SUCCESS] Session cost REPLACED for task_id: cgb11bdb92
[2025-08-11 09:39:35] [SUCCESS] New total_cost: 0.0016074999999999998
[2025-08-11 09:39:35] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0010501999999999998, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:35] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 09:39:35] [SUCCESS] Session metadata updated for task_id: cgb11bdb92
[2025-08-11 09:39:35] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 09:39:35] [SUCCESS] Session cost tracking updated for task_id: cgb11bdb92
[2025-08-11 09:39:35] [SUCCESS] Costs REPLACED - Total: 0.0016074999999999998, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0010501999999999998, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:35] 2025-08-11T09:39:35.685628+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:35.685622+00:00
[2025-08-11 09:39:35] 
[2025-08-11 09:39:35] 🔍 Starting B2B Cost Tracking
[2025-08-11 09:39:35] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[92m09:39:35 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
[92m09:39:35 - LiteLLM:INFO[0m: utils.py:3258 - 
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:LiteLLM:
LiteLLM completion() model= gpt-4.1-nano; provider = openai
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m09:39:38 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
[2025-08-11 09:39:38] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:38] 💰 Total Cost: $0.001607
[2025-08-11 09:39:38] 🆔 Task ID: cgb11bdb92
[2025-08-11 09:39:38] 🏗️ Project ID: 90358
[2025-08-11 09:39:38] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:38] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0010501999999999998, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:38] 
[2025-08-11 09:39:38] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 09:39:38]    Total Users: 13
[2025-08-11 09:39:38]    Organization Cost: $886.106919
[2025-08-11 09:39:38] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:38] 🏗️ Existing project entry found for Project ID: 90358
[2025-08-11 09:39:38] 
[2025-08-11 09:39:38] ================================================================================
[2025-08-11 09:39:38] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 09:39:38] ================================================================================
[2025-08-11 09:39:38] 📋 Task ID: cgb11bdb92
[2025-08-11 09:39:38] 🏗️ Project ID: 90358
[2025-08-11 09:39:38] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:38] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:38] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:38] 🔍 Task Exists in Tracking: True
[2025-08-11 09:39:38] ✅ Actually New Task: False
[2025-08-11 09:39:38] --------------------------------------------------------------------------------
[2025-08-11 09:39:38] 💸 COST BREAKDOWN:
[2025-08-11 09:39:38]    Current Task Cost: $0.001607
[2025-08-11 09:39:38]    Previous Task Cost: $0.001468
[2025-08-11 09:39:38]    Operation: REPLACED
[2025-08-11 09:39:38]    Reason: Continuing same task - replacing previous cost of $0.001468
[2025-08-11 09:39:38] --------------------------------------------------------------------------------
[2025-08-11 09:39:38] 📊 USER COST CHANGES:
[2025-08-11 09:39:38]    Original User Cost: $402.969086
[2025-08-11 09:39:38]    New User Cost: $402.969225
[2025-08-11 09:39:38]    User Cost Delta: $+0.000139
[2025-08-11 09:39:38]    🔄 REPLACE LOGIC: $402.969086 - $0.001468 + $0.001607 = $402.969225
[2025-08-11 09:39:38] --------------------------------------------------------------------------------
[2025-08-11 09:39:38] 💾 LLM Costs Document Updated Successfully
[2025-08-11 09:39:38] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 09:39:38]    LLM Costs - User Cost: $402.969225
[2025-08-11 09:39:38]    LLM Costs - Organization Cost: $886.107058
[2025-08-11 09:39:38] --------------------------------------------------------------------------------
[2025-08-11 09:39:38] 🏢 ORGANIZATION COSTS:
[2025-08-11 09:39:38]    Original Org Cost: $886.106919
[2025-08-11 09:39:38]    New Org Cost: $886.107058
[2025-08-11 09:39:38]    Org Cost Delta: $+0.000139
[2025-08-11 09:39:38] --------------------------------------------------------------------------------
[2025-08-11 09:39:38] 📊 B2B COST UPDATE STATUS:
[2025-08-11 09:39:38]    LLM Costs Updated: ✅ YES
[2025-08-11 09:39:38]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 09:39:38] ================================================================================
[2025-08-11 09:39:38] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 09:39:38] Cost update thread Thread-20 (_worker_loop) completed
[2025-08-11 09:39:38] Starting cost update in thread Thread-18 (_worker_loop)
[2025-08-11 09:39:38] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0011757999999999998, 'TaskExecutionAgent': 0.0005572999999999999}, Total cost: 0.0017330999999999998
[2025-08-11 09:39:38] [DEBUG] Starting session tracking for task_id: cgb11bdb92
[2025-08-11 09:39:38] [DEBUG] Updating session cost for task_id: cgb11bdb92
[2025-08-11 09:39:38] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0011757999999999998, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:38] [DEBUG] Total cost: 0.0017330999999999998
[2025-08-11 09:39:38] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 09:39:38] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 9, 39, 38, 354764), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0011757999999999998, 'TaskExecutionAgent': 0.0005572999999999999}, 'total_cost': 0.0017330999999999998}
[2025-08-11 09:39:38] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 09:39:38] [SUCCESS] Session cost REPLACED for task_id: cgb11bdb92
[2025-08-11 09:39:38] [SUCCESS] New total_cost: 0.0017330999999999998
[2025-08-11 09:39:38] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0011757999999999998, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:38] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 09:39:38] [SUCCESS] Session metadata updated for task_id: cgb11bdb92
[2025-08-11 09:39:38] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 09:39:38] [SUCCESS] Session cost tracking updated for task_id: cgb11bdb92
[2025-08-11 09:39:38] [SUCCESS] Costs REPLACED - Total: 0.0017330999999999998, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0011757999999999998, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:38] 2025-08-11T09:39:38.426768+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:38.426762+00:00
[2025-08-11 09:39:38] 
[2025-08-11 09:39:38] 🔍 Starting B2B Cost Tracking
[2025-08-11 09:39:38] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:38] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:38] 💰 Total Cost: $0.001733
[2025-08-11 09:39:38] 🆔 Task ID: cgb11bdb92
[2025-08-11 09:39:38] 🏗️ Project ID: 90358
[2025-08-11 09:39:38] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:38] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0011757999999999998, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:39:38] 
[2025-08-11 09:39:38] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 09:39:38]    Total Users: 13
[2025-08-11 09:39:38]    Organization Cost: $886.107058
[2025-08-11 09:39:38] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:38] 🏗️ Existing project entry found for Project ID: 90358
[2025-08-11 09:39:38] 
[2025-08-11 09:39:38] ================================================================================
[2025-08-11 09:39:38] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 09:39:38] ================================================================================
[2025-08-11 09:39:38] 📋 Task ID: cgb11bdb92
[2025-08-11 09:39:38] 🏗️ Project ID: 90358
[2025-08-11 09:39:38] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:39:38] 🏢 Organization ID: rdk7542
[2025-08-11 09:39:38] 🆕 Is New Task (Parameter): False
[2025-08-11 09:39:38] 🔍 Task Exists in Tracking: True
[2025-08-11 09:39:38] ✅ Actually New Task: False
[2025-08-11 09:39:38] --------------------------------------------------------------------------------
[2025-08-11 09:39:38] 💸 COST BREAKDOWN:
[2025-08-11 09:39:38]    Current Task Cost: $0.001733
[2025-08-11 09:39:38]    Previous Task Cost: $0.001607
[2025-08-11 09:39:38]    Operation: REPLACED
[2025-08-11 09:39:38]    Reason: Continuing same task - replacing previous cost of $0.001607
[2025-08-11 09:39:38] --------------------------------------------------------------------------------
[2025-08-11 09:39:38] 📊 USER COST CHANGES:
[2025-08-11 09:39:38]    Original User Cost: $402.969225
[2025-08-11 09:39:38]    New User Cost: $402.969351
[2025-08-11 09:39:38]    User Cost Delta: $+0.000126
[2025-08-11 09:39:38]    🔄 REPLACE LOGIC: $402.969225 - $0.001607 + $0.001733 = $402.969351
[2025-08-11 09:39:38] --------------------------------------------------------------------------------
[2025-08-11 09:39:38] 💾 LLM Costs Document Updated Successfully
[2025-08-11 09:39:38] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 09:39:38]    LLM Costs - User Cost: $402.969351
[2025-08-11 09:39:38]    LLM Costs - Organization Cost: $886.107184
[2025-08-11 09:39:38] --------------------------------------------------------------------------------
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m09:39:38 - LiteLLM:INFO[0m: utils.py:1260 - Wrapper: Completed Call, calling success_handler
INFO:LiteLLM:Wrapper: Completed Call, calling success_handler
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
[2025-08-11 09:40:16] 🏢 ORGANIZATION COSTS:
[2025-08-11 09:40:16]    Original Org Cost: $886.107058
[2025-08-11 09:40:16]    New Org Cost: $886.107184
[2025-08-11 09:40:16]    Org Cost Delta: $+0.000126
[2025-08-11 09:40:16] --------------------------------------------------------------------------------
[2025-08-11 09:40:16] 📊 B2B COST UPDATE STATUS:
[2025-08-11 09:40:16]    LLM Costs Updated: ✅ YES
[2025-08-11 09:40:16]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 09:40:16] ================================================================================
[2025-08-11 09:40:16] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 09:40:16] Starting cost update in thread Thread-16 (_worker_loop)
[2025-08-11 09:40:16] Agent cost: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0013570999999999998, 'TaskExecutionAgent': 0.0005572999999999999}, Total cost: 0.0019143999999999997
[2025-08-11 09:40:16] [DEBUG] Starting session tracking for task_id: cgb11bdb92
[2025-08-11 09:40:16] [DEBUG] Updating session cost for task_id: cgb11bdb92
[2025-08-11 09:40:16] [DEBUG] Agent costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0013570999999999998, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:40:16] [DEBUG] Total cost: 0.0019143999999999997
[2025-08-11 09:40:16] [DEBUG] Now updating costs - REPLACING current values
[2025-08-11 09:40:16] [DEBUG] Creating cost history entry: {'timestamp': datetime.datetime(2025, 8, 11, 9, 39, 38, 874268), 'agent_costs': {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0013570999999999998, 'TaskExecutionAgent': 0.0005572999999999999}, 'total_cost': 0.0019143999999999997}
[2025-08-11 09:40:16] [DEBUG] Update result - matched: 1, modified: 1
[2025-08-11 09:40:16] [SUCCESS] Session cost REPLACED for task_id: cgb11bdb92
[2025-08-11 09:40:16] [SUCCESS] New total_cost: 0.0019143999999999997
[2025-08-11 09:40:16] [SUCCESS] New agent_costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0013570999999999998, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:40:16] [DEBUG] Cost update result: {'success': True, 'updated': True}
[2025-08-11 09:40:16] [SUCCESS] Session metadata updated for task_id: cgb11bdb92
[2025-08-11 09:40:16] [DEBUG] Metadata update result: {'success': True}
[2025-08-11 09:40:16] [SUCCESS] Session cost tracking updated for task_id: cgb11bdb92
[2025-08-11 09:40:16] [SUCCESS] Costs REPLACED - Total: 0.0019143999999999997, Agents: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0013570999999999998, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:40:16] 2025-08-11T09:39:38.909740+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:39:38.909735+00:00
[2025-08-11 09:40:16] 
[2025-08-11 09:40:16] 🔍 Starting B2B Cost Tracking
[2025-08-11 09:40:16] 📋 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:40:16] 🏢 Organization ID: rdk7542
[2025-08-11 09:40:16] 💰 Total Cost: $0.001914
[2025-08-11 09:40:16] 🆔 Task ID: cgb11bdb92
[2025-08-11 09:40:16] 🏗️ Project ID: 90358
[2025-08-11 09:40:16] 🆕 Is New Task (Parameter): False
[2025-08-11 09:40:16] 🤖 Agents Costs: {'InitialSetupAgent': 0, 'PlanningAgent': 0, 'CodeWritingAgent': 0, 'TestCodeWritingAgent': 0, 'TestExecutionAgent': 0, 'BugFixingAndVerificationAgent': 0, 'QuestionAnsweringAgent': 0, 'DocumentationAgent': 0, 'GeneralistAgent': 0, 'HelpAgent': 0, 'DesignExtractionAgent': 0, 'SupabaseConfigurationAgent': 0, 'FigmaExtractionAgent': 0, 'WebsiteExtractionAgent': 0, 'Knowledge': 0.0013570999999999998, 'TaskExecutionAgent': 0.0005572999999999999}
[2025-08-11 09:40:16] 
[2025-08-11 09:40:16] 🔍 Existing LLM Cost Document Structure:
[2025-08-11 09:40:16]    Total Users: 13
[2025-08-11 09:40:16]    Organization Cost: $886.107184
[2025-08-11 09:40:16] 👤 Existing user entry found for 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:40:16] 🏗️ Existing project entry found for Project ID: 90358
[2025-08-11 09:40:16] 
[2025-08-11 09:40:16] ================================================================================
[2025-08-11 09:40:16] 💰 COMPREHENSIVE B2B COST TRACKING SUMMARY
[2025-08-11 09:40:16] ================================================================================
[2025-08-11 09:40:16] 📋 Task ID: cgb11bdb92
[2025-08-11 09:40:16] 🏗️ Project ID: 90358
[2025-08-11 09:40:16] 👤 User ID: 14d8e428-5001-701f-dee2-9f12f5ce8c56
[2025-08-11 09:40:16] 🏢 Organization ID: rdk7542
[2025-08-11 09:40:16] 🆕 Is New Task (Parameter): False
[2025-08-11 09:40:16] 🔍 Task Exists in Tracking: True
[2025-08-11 09:40:16] ✅ Actually New Task: False
[2025-08-11 09:40:16] --------------------------------------------------------------------------------
[2025-08-11 09:40:16] 💸 COST BREAKDOWN:
[2025-08-11 09:40:16]    Current Task Cost: $0.001914
[2025-08-11 09:40:16]    Previous Task Cost: $0.001733
[2025-08-11 09:40:16]    Operation: REPLACED
[2025-08-11 09:40:16]    Reason: Continuing same task - replacing previous cost of $0.001733
[2025-08-11 09:40:16] --------------------------------------------------------------------------------
[2025-08-11 09:40:16] 📊 USER COST CHANGES:
[2025-08-11 09:40:16]    Original User Cost: $402.969351
[2025-08-11 09:40:16]    New User Cost: $402.969532
[2025-08-11 09:40:16]    User Cost Delta: $+0.000181
[2025-08-11 09:40:16]    🔄 REPLACE LOGIC: $402.969351 - $0.001733 + $0.001914 = $402.969532
[2025-08-11 09:40:16] --------------------------------------------------------------------------------
[2025-08-11 09:40:16] 💾 LLM Costs Document Updated Successfully
[2025-08-11 09:40:16] 🔍 FINAL B2B COST VERIFICATION:
[2025-08-11 09:40:16]    LLM Costs - User Cost: $402.969532
[2025-08-11 09:40:16]    LLM Costs - Organization Cost: $886.107365
[2025-08-11 09:40:16] --------------------------------------------------------------------------------
[2025-08-11 09:40:16] 🏢 ORGANIZATION COSTS:
[2025-08-11 09:40:16]    Original Org Cost: $886.107184
[2025-08-11 09:40:16]    New Org Cost: $886.107365
[2025-08-11 09:40:16]    Org Cost Delta: $+0.000181
[2025-08-11 09:40:16] --------------------------------------------------------------------------------
[2025-08-11 09:40:16] 📊 B2B COST UPDATE STATUS:
[2025-08-11 09:40:16]    LLM Costs Updated: ✅ YES
[2025-08-11 09:40:16]    Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)
[2025-08-11 09:40:16] ================================================================================
[2025-08-11 09:40:16] ✅ B2B Cost Tracking Completed Successfully
[2025-08-11 09:40:16] Cost update thread Thread-18 (_worker_loop) completed
[2025-08-11 09:40:16] Cost update thread Thread-16 (_worker_loop) completed
[2025-08-11 09:40:16] 2025-08-11T09:40:16.425353+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:16.425344+00:00
[2025-08-11 09:40:16] *****Calling html_with_assett****
[2025-08-11 09:40:16] *******************************************************
[2025-08-11 09:40:16] 2025-08-11T09:40:16.426406+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:16] 2025-08-11T09:40:16.426433+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 09:40:16] 2025-08-11T09:40:16.426619+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:16] 2025-08-11T09:40:16.426713+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:16] 2025-08-11T09:40:16.426740+00:00 HTML file not found in fallback location either
[2025-08-11 09:40:16] 2025-08-11T09:40:16.528089+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:16.528082+00:00
[2025-08-11 09:40:16] *****Calling html_with_assett****
[2025-08-11 09:40:16] *******************************************************
[2025-08-11 09:40:16] 2025-08-11T09:40:16.529536+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:16] 2025-08-11T09:40:16.529558+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 09:40:16] 2025-08-11T09:40:16.529582+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:16] 2025-08-11T09:40:16.529646+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:16] 2025-08-11T09:40:16.529673+00:00 HTML file not found in fallback location either
[2025-08-11 09:40:16] 2025-08-11T09:40:16.630926+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:16.630921+00:00
[2025-08-11 09:40:16] *****Calling html_with_assett****
[2025-08-11 09:40:16] *******************************************************
[2025-08-11 09:40:19] 2025-08-11T09:40:16.631675+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:16.631945+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 09:40:19] 2025-08-11T09:40:16.631993+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:16.632156+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:16.632200+00:00 HTML file not found in fallback location either
[2025-08-11 09:40:19] 2025-08-11T09:40:16.733722+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:16.733716+00:00
[2025-08-11 09:40:19] *****Calling html_with_assett****
[2025-08-11 09:40:19] *******************************************************
[2025-08-11 09:40:19] 2025-08-11T09:40:16.734739+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:16.734757+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 09:40:19] 2025-08-11T09:40:16.734781+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:16.734855+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:16.734882+00:00 HTML file not found in fallback location either
[2025-08-11 09:40:19] 2025-08-11T09:40:16.945733+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:16.945727+00:00
[2025-08-11 09:40:19] *****Calling html_with_assett****
[2025-08-11 09:40:19] *******************************************************
[2025-08-11 09:40:19] 2025-08-11T09:40:16.946866+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:16.946889+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 09:40:19] 2025-08-11T09:40:16.946914+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:16.946978+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:16.947004+00:00 HTML file not found in fallback location either
[2025-08-11 09:40:19] 2025-08-11T09:40:17.049582+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:17.049575+00:00
[2025-08-11 09:40:19] *****Calling html_with_assett****
[2025-08-11 09:40:19] *******************************************************
[2025-08-11 09:40:19] 2025-08-11T09:40:17.052645+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:17.052670+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 09:40:19] 2025-08-11T09:40:17.052710+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:17.052915+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:17.052940+00:00 HTML file not found in fallback location either
[2025-08-11 09:40:19] 2025-08-11T09:40:18.942114+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:18.942108+00:00
[2025-08-11 09:40:19] Received generate_figma_screen_code request for screen: TODO PAGE
[2025-08-11 09:40:19] Screen ID: 9:680
[2025-08-11 09:40:19] 2025-08-11T09:40:18.956713+00:00 Looking for screen file: screen_9:680.json
[2025-08-11 09:40:19] 2025-08-11T09:40:19.002899+00:00 Successfully copied screen_9:680.json from temp-attachments to attachments
[2025-08-11 09:40:19] 2025-08-11T09:40:19.002927+00:00 Copied 1 file(s) for screen 'TODO PAGE' (ID: 9:680)
[2025-08-11 09:40:19] Sending auto-generated message: Could you convert this design to code
[2025-08-11 09:40:19] 2025-08-11T09:40:19.004080+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:19.004074+00:00
[2025-08-11 09:40:19] 2025-08-11T09:40:19.005333+00:00 HANDLING USER MESSAGE: Could you convert this design to code
[2025-08-11 09:40:19] 2025-08-11T09:40:19.006752+00:00 Successfully synced attachments folder from /efs/rdk7542/90358/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 09:40:19] Starting HTML generation monitor for screen: TODO PAGE
[2025-08-11 09:40:19] Expected HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:19.014711+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90358/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 09:40:19] Extra None
[2025-08-11 09:40:19] Processing attachments [{'attachment_id': 'screen_9:680.json', 'file_type': 'figma_json', 'name': 'TODO PAGE'}]
[2025-08-11 09:40:19] Error processing attachment: can only concatenate str (not "NoneType") to str
[2025-08-11 09:40:19] Attachments sent to chat interface []
[2025-08-11 09:40:19] 2025-08-11T09:40:19.017872+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:19.017867+00:00
[2025-08-11 09:40:19] Message added ChatMessage(id=5e803503-9f8e-4713-942c-a9c1fdde29e8, type = user content=Could you convert this design to code, status=MessageStatus.PENDING), parent_id=None), metadata={'user_id': None}), timestamp=2025-08-11 09:40:19.017258),requires_resolution=True), extra=None)
[2025-08-11 09:40:19] Message dict {'id': '5e803503-9f8e-4713-942c-a9c1fdde29e8', 'content': 'Could you convert this design to code', 'msg_type': 'user', 'timestamp': '2025-08-11T09:40:19.017258+00:00', 'status': 'pending', 'requires_resolution': True, 'resolution_id': None, 'parent_id': None, 'metadata': {'user_id': None}, 'attachments': [], 'extra': None}
[2025-08-11 09:40:19] DEBUG: Disabling task creation
[2025-08-11 09:40:19] DEBUG: Full request: SystemChatRequest(message_id='5e803503-9f8e-4713-942c-a9c1fdde29e8', content='Could you convert this design to code', response_queue=<Queue at 0x7fe77315e310 maxsize=0>, parent_id=None, attachments=[], extra=None)
[2025-08-11 09:40:19] Parent ID: None Request ID: 5e803503-9f8e-4713-942c-a9c1fdde29e8
[2025-08-11 09:40:19] 2025-08-11T09:40:19.019711+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:19.019704+00:00
[2025-08-11 09:40:19] 2025-08-11T09:40:19.019881+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:19.019877+00:00
[2025-08-11 09:40:19] Message added ChatMessage(id=5e803503-9f8e-4713-942c-a9c1fdde29e8, type = user content=Could you convert this design to code, status=MessageStatus.PENDING), parent_id=None), metadata={'user_id': None}), timestamp=2025-08-11 09:40:19.017258+00:00),requires_resolution=True), extra=None)
[2025-08-11 09:40:19] Message added ChatMessage(id=cb9026e3-99e4-4b5a-9541-6d7201034d8a, type = system content=Resolved user message, status=MessageStatus.COMPLETED), parent_id=5e803503-9f8e-4713-942c-a9c1fdde29e8), metadata=None), timestamp=2025-08-11 09:40:19.018951),requires_resolution=False), extra={})
[2025-08-11 09:40:19] Message dict {'id': '5e803503-9f8e-4713-942c-a9c1fdde29e8', 'content': 'Could you convert this design to code', 'msg_type': 'user', 'timestamp': '2025-08-11T09:40:19.017258+00:00', 'status': 'pending', 'requires_resolution': True, 'resolution_id': 'cb9026e3-99e4-4b5a-9541-6d7201034d8a', 'parent_id': None, 'metadata': {'user_id': None}, 'attachments': [], 'extra': None}
[2025-08-11 09:40:19] Message dict {'id': 'cb9026e3-99e4-4b5a-9541-6d7201034d8a', 'content': 'Resolved user message', 'msg_type': 'system', 'timestamp': '2025-08-11T09:40:19.018951+00:00', 'status': 'completed', 'requires_resolution': False, 'resolution_id': None, 'parent_id': '5e803503-9f8e-4713-942c-a9c1fdde29e8', 'metadata': None, 'attachments': None, 'extra': {}}
[2025-08-11 09:40:19] 2025-08-11T09:40:19.024561+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:19.024554+00:00
[2025-08-11 09:40:19] Message resolved ChatMessage(id=5e803503-9f8e-4713-942c-a9c1fdde29e8, type = user content=Could you convert this design to code, status=MessageStatus.RESOLVED), parent_id=None), metadata={'user_id': None}), timestamp=2025-08-11 09:40:19.017258+00:00),requires_resolution=True), extra=None)
[2025-08-11 09:40:19] 2025-08-11T09:40:19.057757+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90358/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 09:40:19] 2025-08-11T09:40:19.077097+00:00 Successfully synced attachments folder from /efs/rdk7542/90358/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 09:40:19] 2025-08-11T09:40:19.844072+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:19.844066+00:00
[2025-08-11 09:40:19] *****Calling html_with_assett****
[2025-08-11 09:40:19] *******************************************************
[2025-08-11 09:40:19] 2025-08-11T09:40:19.845375+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:19.845396+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 09:40:19] 2025-08-11T09:40:19.845418+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:19.845521+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:19] 2025-08-11T09:40:19.845548+00:00 HTML file not found in fallback location either
[2025-08-11 09:40:22] 2025-08-11T09:40:19.906672+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90358/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 09:40:22] 2025-08-11T09:40:19.908653+00:00 Successfully synced attachments folder from /efs/rdk7542/90358/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 09:40:22] Getting NodeDB connection for tenant: developrdk7542
[2025-08-11 09:40:22] query---> MATCH (n) WHERE ID(n) = $node_id SET n.Manifest = $Manifest RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties 
[2025-08-11 09:40:22] params--->> {'node_id': 90358, 'Manifest': '{"overview": {"project_name": "todo_app", "description": "A simple todo application allowing users to add, view, edit, and delete their tasks.", "third_party_services": [], "env": {}}, "containers": [{"container_name": "todo_frontend", "description": "Provides the user interface for managing todo items.", "interfaces": "User interface for creating, viewing, editing, and deleting todos", "container_type": "frontend", "dependent_containers": ["todo_database"], "workspace": "simple-todo-list-90358-90367", "container_root": "simple-todo-list-90358-90367/todo_frontend", "port": 3000, "framework": "react", "type": "", "buildCommand": "npm install && npx tsc --noEmit && npm test -- --ci", "startCommand": "PORT=<port> HOST=<host> BROWSER=none npm start", "installCommand": "npm install", "lintCommand": "./../.init/.linter.sh", "generateOpenapiCommand": "", "container_details": {"features": ["Add todo item", "View todo list", "Edit todo item", "Delete todo item", "Mark item as completed"], "colors": {"primary": "#1e90ff", "secondary": "#ff9800", "accent": "#4caf50"}, "theme": "light", "layout_description": "A central list displaying todos with input at the top or modal for adding/editing, and buttons for actions next to each item.", "style": "minimalistic"}, "lintConfig": "", "routes": [], "apiSpec": "", "auth": null, "schema": "", "migrations": "", "seed": "", "env": {}}]}'}
[2025-08-11 09:40:22] 2025-08-11T09:40:19.947504+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:19.947498+00:00
[2025-08-11 09:40:22] *****Calling html_with_assett****
[2025-08-11 09:40:22] *******************************************************
[2025-08-11 09:40:22] 2025-08-11T09:40:19.948814+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:19.948833+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 09:40:22] 2025-08-11T09:40:19.948871+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:19.948960+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:19.948999+00:00 HTML file not found in fallback location either
[2025-08-11 09:40:22] 2025-08-11T09:40:19.998271+00:00 Successfully synced attachments folder from /efs/rdk7542/90358/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 09:40:22] 2025-08-11T09:40:20.005252+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90358/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 09:40:22] 2025-08-11T09:40:20.050253+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:20.050247+00:00
[2025-08-11 09:40:22] *****Calling html_with_assett****
[2025-08-11 09:40:22] *******************************************************
[2025-08-11 09:40:22] 2025-08-11T09:40:20.051213+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:20.051232+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 09:40:22] 2025-08-11T09:40:20.051269+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:20.051719+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:20.051775+00:00 HTML file not found in fallback location either
[2025-08-11 09:40:22] 2025-08-11T09:40:20.107759+00:00 Successfully synced attachments folder from /efs/rdk7542/90358/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 09:40:22] 2025-08-11T09:40:20.111590+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90358/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 09:40:22] 2025-08-11T09:40:20.153197+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:20.153191+00:00
[2025-08-11 09:40:22] *****Calling html_with_assett****
[2025-08-11 09:40:22] *******************************************************
[2025-08-11 09:40:22] 2025-08-11T09:40:20.154324+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:20.154344+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 09:40:22] 2025-08-11T09:40:20.154375+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:20.154478+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:20.154515+00:00 HTML file not found in fallback location either
[2025-08-11 09:40:22] 2025-08-11T09:40:20.207825+00:00 Successfully synced attachments folder from /efs/rdk7542/90358/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 09:40:22] 2025-08-11T09:40:20.208331+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90358/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 09:40:22] 2025-08-11T09:40:20.372371+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:20.372365+00:00
[2025-08-11 09:40:22] *****Calling html_with_assett****
[2025-08-11 09:40:22] *******************************************************
[2025-08-11 09:40:22] 2025-08-11T09:40:20.373580+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:20.373646+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 09:40:22] 2025-08-11T09:40:20.373670+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:20.373767+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:20.373791+00:00 HTML file not found in fallback location either
[2025-08-11 09:40:22] 2025-08-11T09:40:20.439920+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90358/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 09:40:22] 2025-08-11T09:40:20.452697+00:00 Successfully synced attachments folder from /efs/rdk7542/90358/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 09:40:22] 2025-08-11T09:40:20.475817+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:20.475811+00:00
[2025-08-11 09:40:22] *****Calling html_with_assett****
[2025-08-11 09:40:22] *******************************************************
[2025-08-11 09:40:22] 2025-08-11T09:40:20.478317+00:00 Processing HTML file: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:20.478341+00:00 Assets directory: /home/<USER>/workspace/code-generation/assets
[2025-08-11 09:40:22] 2025-08-11T09:40:20.478361+00:00 HTML file not found: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:20.478410+00:00 Trying fallback path: /home/<USER>/workspace/code-generation/assets/todo-page.html
[2025-08-11 09:40:22] 2025-08-11T09:40:20.478428+00:00 HTML file not found in fallback location either
[2025-08-11 09:40:22] 2025-08-11T09:40:20.552513+00:00 Successfully synced figmafiles folder from /efs/rdk7542/90358/workspace/figmafiles/ to /home/<USER>/workspace/code-generation/figmafiles
[2025-08-11 09:40:22] 2025-08-11T09:40:20.556105+00:00 Successfully synced attachments folder from /efs/rdk7542/90358/workspace/attachments/ to /home/<USER>/workspace/code-generation/attachments
[2025-08-11 09:40:22] NoneType: None
[2025-08-11 09:40:22] 
[2025-08-11 09:40:22] 2025-08-11T09:40:22.263731+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:22.263727+00:00
[2025-08-11 09:40:22] Message added ChatMessage(id=3cc5611d-de7f-43b0-be27-3cae43c793ee, type = command content=Preview for todo_frontend is now available at: https://vscode-internal-39097-dev.dev01.cloud.kavia.ai:4000/preview.html, status=MessageStatus.COMPLETED), parent_id=None), metadata=None), timestamp=2025-08-11 09:40:22.263492),requires_resolution=False), extra={})
[2025-08-11 09:40:22] Message dict {'id': '3cc5611d-de7f-43b0-be27-3cae43c793ee', 'content': 'Preview for todo_frontend is now available at: https://vscode-internal-39097-dev.dev01.cloud.kavia.ai:4000/preview.html', 'msg_type': 'command', 'timestamp': '2025-08-11T09:40:22.263492+00:00', 'status': 'completed', 'requires_resolution': False, 'resolution_id': None, 'parent_id': None, 'metadata': None, 'attachments': [], 'extra': {}}
INFO:     Started server process [476]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:4000 (Press CTRL+C to quit)
[2025-08-11 09:40:23] 2025-08-11T09:40:22.265206+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:22.265202+00:00
[2025-08-11 09:40:23] Message dict {'id': '7c8e603a-e98f-4eaf-830f-080a0c26eb93', 'content': 'Resolved user message', 'msg_type': 'system', 'timestamp': '2025-08-11T09:40:22.264990+00:00', 'status': 'completed', 'requires_resolution': False, 'resolution_id': None, 'parent_id': '3cc5611d-de7f-43b0-be27-3cae43c793ee', 'metadata': None, 'attachments': None, 'extra': {}}
[2025-08-11 09:40:23] 2025-08-11T09:40:22.267122+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:22.267117+00:00
[2025-08-11 09:40:23] Message resolved ChatMessage(id=3cc5611d-de7f-43b0-be27-3cae43c793ee, type = command content=Preview for todo_frontend is now available at: https://vscode-internal-39097-dev.dev01.cloud.kavia.ai:4000/preview.html, status=MessageStatus.RESOLVED), parent_id=None), metadata=None), timestamp=2025-08-11 09:40:22.263492+00:00),requires_resolution=False), extra={})
[2025-08-11 09:40:23] Starting app state callback in thread _app_state_callback_thread_daemon_140633335395904
[2025-08-11 09:40:23] 🎯 First time app state callback triggered - initiating commit changes
[2025-08-11 09:40:23] ✅ First-time commit changes initiated successfully
[2025-08-11 09:40:23] 2025-08-11T09:40:22.272507+00:00 Activity updated for task cgb11bdb92 at 2025-08-11 09:40:22.272502+00:00
[2025-08-11 09:40:23] Preview URL and state https://vscode-internal-39097-dev.dev01.cloud.kavia.ai:4000/preview.html running
[2025-08-11 09:40:23] Found 1 containers for task cgb11bdb92
[2025-08-11 09:40:23] Containers [{'status': 'running', 'timestamp': '2025-08-11T09:40:22.223359', 'url': 'https://vscode-internal-39097-dev.dev01.cloud.kavia.ai:3000/preview.html', 'api_route': '', 'db_env_vars': None, 'error': None, 'failed_dependencies': [], 'warning': None, 'name': 'todo_frontend', 'container_type': 'web', 'framework': 'react'}]
[2025-08-11 09:40:23] App state callback thread _app_state_callback_thread_daemon_140633335395904 completed
[2025-08-11 09:40:23] INFO:     10.1.8.62:33916 - "HEAD /preview.html HTTP/1.1" 200 OK
[2025-08-11 09:40:23]           }
[2025-08-11 09:40:23]               });
[2025-08-11 09:40:23]             }
[2025-08-11 09:40:23]           }
[2025-08-11 09:40:23]           vidPreview = document.getElementById('vid-preview');
[2025-08-11 09:40:23]           vidPreview.src = e.target.value;
[2025-08-11 09:40:23]         });
[2025-08-11 09:40:23]       }
[2025-08-11 09:40:23]     }
[2025-08-11 09:40:23] 
[2025-08-11 09:40:23]     function mapSelectorValue(selector_id, current_value) {
[2025-08-11 09:40:23]       mapped_value = current_value;
[2025-08-11 09:40:23]  INFO:     10.1.8.62:33918 - "HEAD /preview.html HTTP/1.1" 200 OK