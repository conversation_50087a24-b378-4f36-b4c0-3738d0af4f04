import React, { useState, useEffect, useCallback, useRef, useContext } from "react";
import {
  Loader2,
  <PERSON>fresh<PERSON><PERSON>,
  AlertTriangle,
  AlertCircle,
  Square,
  ChevronDown,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Monitor,
  FileX,
  Play,
  Code,
} from "lucide-react";
import { Sandpack } from "@codesandbox/sandpack-react";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import useAppetize from "@/components/CodeGenrationPanel/PreviewPanel/Appetize/useAppetize";
import AppetizeLogs from "@/components/CodeGenrationPanel/PreviewPanel/Appetize/AppetizeLogs";
import { ContainerStatusTypes } from "@/components/CodeGenrationPanel/PreviewPanel/ContainerTypes";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { useParams } from "next/navigation";
import Cookies from "js-cookie";

/**
 * Changes port from 3000 to 4000 in preview URLs
 * @param {string} url - The URL to process
 * @returns {string} - The URL with updated port
 */
const processPreviewUrl = (url) => {
  if (!url || typeof url !== 'string') return url;
  // Replace any occurrence of :3000 with :4000 in the URL
  return url.replace(':3000', ':4000');
};

/**
 * Converts screen name to HTML filename
 * @param {string} screenName - The screen name (e.g., "TODO PAGE")
 * @returns {string} - The HTML filename (e.g., "todo-page.html")
 */
const convertScreenNameToFileName = (screenName) => {
  if (!screenName || typeof screenName !== 'string') return '';
  return screenName
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
    + '.html';
};

/**
 * Sends WebSocket message to fetch HTML content
 * @param {WebSocket} wsConnection - WebSocket connection
 * @param {string} fileName - HTML filename
 */
const fetchFigmaHTMLViaWebSocket = (wsConnection, fileName) => {
  if (wsConnection?.readyState === WebSocket.OPEN) {
    console.error("Sending WebSocket message for HTML fetch");
    const message = {
      type: 'fetch_asset_screen',
      filename: fileName,
    };

    console.error('Sending WebSocket message:', message);
    wsConnection.send(JSON.stringify(message));
    return true;
  } else {
    console.warn('WebSocket connection is not open, readyState:', wsConnection?.readyState);
    return false;
  }
};

const APPETIZE_INIT_DELAY = 6000;

const FigmaPreviewPanel = ({
  currentTaskId,
  customUrl = "",
  showMode = "split",
  figmaData = [],
  
}) => {
  const {
    wsConnection,
    containers,
    selectedContainer,
    setSelectedContainer,
    setUploadedAttachments,
    setInputValue,
    isAiTyping,
    activeReplyTo // Added this import
  } = useCodeGeneration();

  const [iframeKey, setIframeKey] = useState(Date.now());
  const [iframeError, setIframeError] = useState(false);
  const [errorType, setErrorType] = useState(null);
  const [isCheckingUrl, setIsCheckingUrl] = useState(false);
  const [urlStatus, setUrlStatus] = useState('checking');
  const [customIframeKey, setCustomIframeKey] = useState(Date.now());

  // Figma-specific states - Fixed initialization
  const [selectedScreen, setSelectedScreen] = useState(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const [useAllScreens, setUseAllScreens] = useState(false);

  // New states for HTML content
  const [htmlContent, setHtmlContent] = useState(null);
  const [isLoadingHTML, setIsLoadingHTML] = useState(false);
  const [htmlError, setHtmlError] = useState(null);
  const [showSandpack, setShowSandpack] = useState(false);
  const [pendingHtmlRequest, setPendingHtmlRequest] = useState(null); // Track pending requests

  const { showAlert } = useContext(AlertContext);
    const { projectId } = useParams();
    const tenantId = Cookies.get("tenant_id");

  const iframeRef = useRef(null);
  const customIframeRef = useRef(null);
  const dropdownRef = useRef(null);
  const imageContainerRef = useRef(null);
  const wsMessageHandlerRef = useRef(null); // Add ref to track message handler

  // Appetize integration
  const {
    appetizeState,
    isAppetizeUrl,
    ensureDebugUrl,
    initializeAppetizeClient,
    clearLogs,
    updateUrl
  } = useAppetize();

  // Fetch HTML content for selected screen via WebSocket
  const fetchScreenHTML = useCallback(async (screen) => {
    if (!screen || !projectId || !tenantId) {
      console.error('fetchScreenHTML: Skipping due to missing params or AI typing');
      return;
    }

    const fileName = convertScreenNameToFileName(screen.screen_name || screen.name);
    if (!fileName) {
      console.warn('Could not generate filename from screen name:', screen.screen_name || screen.name);
      return;
    }

    console.error('fetchScreenHTML: Starting fetch for screen:', screen.screen_name || screen.name);
    setIsLoadingHTML(true);
    setHtmlError(null);
    setShowSandpack(false);
    setPendingHtmlRequest(fileName); // Track this request

    try {
      console.log('Fetching HTML for screen:', screen.screen_name || screen.name, 'filename:', fileName);

      const success = fetchFigmaHTMLViaWebSocket(wsConnection, fileName);

      if (!success) {
        throw new Error('Failed to send WebSocket message - connection not ready');
      }

      console.log('WebSocket message sent successfully, waiting for response...');
      // The response will be handled in the WebSocket message listener
      // Don't set loading to false here - wait for WebSocket response
    } catch (error) {
      console.error('Error requesting HTML for screen:', screen.screen_name || screen.name, error);
      setHtmlError(error.message);
      setHtmlContent(null);
      setShowSandpack(false);
      setIsLoadingHTML(false);
      setPendingHtmlRequest(null);
    }
  }, [projectId, tenantId, wsConnection]);

  // Handle play icon click
  const handlePlayIconClick = useCallback(() => {
    if (!selectedScreen) {
      console.warn('FigmaPreviewPanel: No screen selected');
      return;
    }

    try {
      const screen_name = selectedScreen.screen_name || selectedScreen.name;
      const screen_id = selectedScreen.screen_id || selectedScreen.id;

      if (!screen_name || !screen_id) {
        console.error('FigmaPreviewPanel: Selected screen missing name or id', selectedScreen);
        return;
      }

      // Send generate_figma_screen_code message for the specific selected screen
      const messageData = {
        type: "generate_figma_screen_code",
        screen_name: screen_name,
        screen_id: screen_id
      };

      console.log('Sending generate_figma_screen_code message via WebSocket:', messageData);

      if (wsConnection?.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify(messageData));
        console.log('Generate figma screen code WebSocket message sent successfully');

        // Set loading state to show user that generation is in progress
        setIsLoadingHTML(true);
        setHtmlError(null);
      } else {
        console.warn('WebSocket not ready for generate_figma_screen_code message, readyState:', wsConnection?.readyState);
      }

    } catch (error) {
      console.error('FigmaPreviewPanel: Error handling play icon click:', error);
    }
  }, [selectedScreen, wsConnection]);

  // WebSocket message handler
  const handleWebSocketMessage = useCallback((event) => {
    try {
      const data = JSON.parse(event.data);
      console.error('Received WebSocket message:', data.type, data);

      if (data.type === 'fetch_asset_screen_response') {
        console.error('Received fetch_asset_screen_response:', data);

        // Only handle responses if we have a pending request
        if (!pendingHtmlRequest) {
          console.log('No pending HTML request, ignoring response');
          return;
        }

        // Extract the nested data
        const responseData = data.data || {};

        // Clear loading state and pending request
        setIsLoadingHTML(false);
        setPendingHtmlRequest(null);

        // Case 1: Success - HTML content is available
        if (responseData.html_content) {
          console.error('HTML content received, showing in Sandpack');
          setHtmlContent(responseData.html_content);
          setShowSandpack(true);
          setHtmlError(null);
          return;
        }

        // Case 2: Error - No HTML file available, show play icon
        if (responseData.error) {
          console.error('Error response received:', responseData.error);
          setHtmlContent(null);
          setShowSandpack(false);
          setHtmlError(null); // Clear error to show play icon
          return;
        }

        // Fallback: If neither html_content nor error, treat as error (show play icon)
        console.error('Unexpected response format, showing play icon');
        setHtmlContent(null);
        setShowSandpack(false);
        setHtmlError(null);
      }

      // Handle generate_figma_screen_code responses
      if (data.type === 'success' && data.data?.message?.includes('Started code generation')) {
        console.log('Code generation started successfully:', data.data);
        // Keep loading state active - HTML will be generated in background
      }

      if (data.type === 'error' && data.data?.error?.includes('screen')) {
        console.error('Code generation error:', data.data.error);
        setIsLoadingHTML(false);
        setHtmlError(data.data.error);
      }

      // Handle HTML generation completion
      if (data.type === 'figma_html_generated') {
        console.log('HTML generation completed:', data.data);
        // Automatically fetch the generated HTML
        if (selectedScreen) {
          setTimeout(() => {
            fetchScreenHTML(selectedScreen);
          }, 1000); // Small delay to ensure file is ready
        }
      }

    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }, [pendingHtmlRequest, selectedScreen, fetchScreenHTML]);

  // WebSocket message listener setup
  useEffect(() => {
    if (!wsConnection) {
      console.log('No WebSocket connection available');
      return;
    }

    console.error('Setting up WebSocket message listener, connection state:', wsConnection.readyState);

    // Remove previous event listener if it exists
    if (wsMessageHandlerRef.current) {
      console.error('Removing previous WebSocket message listener');
      wsConnection.removeEventListener('message', wsMessageHandlerRef.current);
    }

    // Store the handler reference
    wsMessageHandlerRef.current = handleWebSocketMessage;

    // Add new event listener
    wsConnection.addEventListener('message', handleWebSocketMessage);
    console.log('WebSocket message listener added successfully');

    // Cleanup function
    return () => {
      if (wsConnection && wsMessageHandlerRef.current) {
        console.log('Cleaning up WebSocket message listener');
        wsConnection.removeEventListener('message', wsMessageHandlerRef.current);
        wsMessageHandlerRef.current = null;
      }
    };
  }, [wsConnection, handleWebSocketMessage]);

  // Update selectedScreen when figmaData changes
  useEffect(() => {
    if (figmaData?.length > 0 && !selectedScreen) {
      console.log('Setting initial selected screen:', figmaData[0]);
      setSelectedScreen(figmaData[0]);
    } else if (figmaData?.length === 0) {
      console.log('Clearing selected screen - no figma data');
      setSelectedScreen(null);
    }
  }, [figmaData, selectedScreen]);

  // Fetch HTML when selectedScreen changes
  useEffect(() => {
    if (selectedScreen && !isAiTyping) {
      console.error('Selected screen changed, fetching HTML:', selectedScreen.screen_name || selectedScreen.name);
      fetchScreenHTML(selectedScreen);
    } else {
      console.error('Skipping HTML fetch:', {
        hasSelectedScreen: !!selectedScreen,
        isAiTyping,
        wsConnectionReady: wsConnection?.readyState === WebSocket.OPEN
      });
    }
  }, [selectedScreen, fetchScreenHTML, isAiTyping, wsConnection]);

  // Watch isAiTyping to refetch HTML when AI stops typing
  useEffect(() => {
    if (!isAiTyping && selectedScreen) {
      console.error('AI stopped typing, refetching HTML after delay');
      // Add a small delay to ensure any backend processing is complete

      fetchScreenHTML(selectedScreen);

    }
  }, [isAiTyping, selectedScreen, fetchScreenHTML, wsConnection]);

  // Watch WebSocket connection state
  useEffect(() => {
    if (wsConnection) {
      console.log('WebSocket connection state changed:', wsConnection.readyState);

      // If connection just became ready and we have a selected screen, fetch HTML
      if (wsConnection.readyState === WebSocket.OPEN && selectedScreen && !isAiTyping) {
        console.log('WebSocket connection ready, fetching HTML for current screen');
        setTimeout(() => {
          fetchScreenHTML(selectedScreen);
        }, 500); // Small delay to ensure connection is stable
      }
    }
  }, [wsConnection, selectedScreen, isAiTyping, fetchScreenHTML]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Auto-select first container if none is selected
  useEffect(() => {
    if (!selectedContainer && containers.length > 0) {
      setSelectedContainer(containers[0].name);
    }
  }, [selectedContainer, containers, setSelectedContainer]);

  // Find the selected container object
  const getSelectedContainerObj = useCallback(() => {
    return containers.find(c => c.name === selectedContainer);
  }, [containers, selectedContainer]);

  // Check URL accessibility before showing iframe
  const checkUrlAccessibility = useCallback(async (url) => {
    // Skip URL accessibility check for Appetize URLs to avoid CORS issues
    if (isAppetizeUrl(url)) {
      return { accessible: true, status: 200, isAppetize: true };
    }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);

      if (response.status === 404) {
        return { accessible: false, status: 404, error: 'Not Found' };
      } else if (response.status === 502) {
        return { accessible: false, status: 502, error: 'Bad Gateway' };
      } else if (response.ok) {
        return { accessible: true, status: response.status };
      } else {
        return { accessible: false, status: response.status, error: response.statusText };
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        return { accessible: false, status: 'timeout', error: 'Request timeout' };
      }

      // Handle CORS errors - if it's a CORS error, assume URL is accessible
      if (error.message.includes('CORS') ||
        error.message.includes('cross-origin') ||
        error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        return { accessible: true, status: 200, corsBlocked: true };
      }

      return { accessible: false, status: 'network', error: error.message };
    }
  }, [isAppetizeUrl]);

  // Restart container method
  const restartContainer = useCallback((containerName = selectedContainer) => {
    if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
      wsConnection.send(JSON.stringify({
        type: "restart_container",
        task_id: currentTaskId,
        input_data: {
          container_name: containerName
        }
      }));
    }
  }, [wsConnection, currentTaskId, selectedContainer]);

  // Start container method (alias for restart)
  const startContainer = useCallback((containerName = selectedContainer) => {
    restartContainer(containerName);
  }, [restartContainer, selectedContainer]);

  // Check URL before loading iframe (no auto-retry)
  const handleUrlCheck = useCallback(async (url) => {
    if (!url) return;

    setIsCheckingUrl(true);
    setUrlStatus('checking');

    const result = await checkUrlAccessibility(url);

    if (result.accessible) {
      setUrlStatus('success');
      setIframeError(false);
      setErrorType(null);
    } else {
      setUrlStatus('error');
      setIframeError(true);

      if (result.status === 404) {
        setErrorType('404');
      } else if (result.status === 502) {
        setErrorType('502');
      } else {
        setErrorType('other');
      }
    }

    setIsCheckingUrl(false);
  }, [checkUrlAccessibility]);

  // Manual refresh function for main iframe
  const refreshIframe = useCallback(() => {
    // Clear all states
    setIframeError(false);
    setErrorType(null);
    setIsCheckingUrl(false);
    setUrlStatus('checking');
    setIframeKey(Date.now());

    // Re-check URL
    const container = getSelectedContainerObj();
    if (container?.url) {
      setTimeout(() => {
        handleUrlCheck(processPreviewUrl(container.url));
      }, 100);
    }
  }, [handleUrlCheck, getSelectedContainerObj]);

  // Manual refresh function for custom iframe
  const refreshCustomIframe = useCallback(() => {
    setCustomIframeKey(Date.now());
  }, []);

  // Zoom functions
  const zoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(prev + 0.25, 3));
  }, []);

  const zoomOut = useCallback(() => {
    setZoomLevel(prev => Math.max(prev - 0.25, 0.25));
  }, []);

  const resetZoom = useCallback(() => {
    setZoomLevel(1);
    setImagePosition({ x: 0, y: 0 });
  }, []);

  // Drag functions
  const handleMouseDown = useCallback((e) => {
    if (zoomLevel > 1) {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - imagePosition.x,
        y: e.clientY - imagePosition.y
      });
    }
  }, [zoomLevel, imagePosition]);

  const handleMouseMove = useCallback((e) => {
    if (isDragging) {
      setImagePosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  }, [isDragging, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Update custom iframe when URL changes
  useEffect(() => {
    if (customUrl) {
      setCustomIframeKey(Date.now());
    }
  }, [customUrl]);

  // Reset states when container changes
  useEffect(() => {
    setIframeError(false);
    setErrorType(null);
    setIsCheckingUrl(false);
    setUrlStatus('checking');
    setIframeKey(Date.now());
  }, [selectedContainer]);

  // Check URL when container becomes available and update Appetize
  useEffect(() => {
    const container = getSelectedContainerObj();
    if (container?.status === 'running' && container.url) {
      const processedUrl = processPreviewUrl(container.url);
      handleUrlCheck(processedUrl);

      // Update Appetize URL detection
      updateUrl(processedUrl);
    }
  }, [getSelectedContainerObj, handleUrlCheck, updateUrl]);

  // Initialize Appetize client when iframe loads
  useEffect(() => {
    if (appetizeState.isAppetizeUrl &&
      urlStatus === 'success' &&
      iframeRef.current &&
      !appetizeState.client &&
      !appetizeState.isInitializing) {

      console.log('Initializing Appetize client...');

      const timer = setTimeout(() => {
        initializeAppetizeClient(iframeRef.current);
      }, APPETIZE_INIT_DELAY);

      return () => clearTimeout(timer);
    }
  }, [
    appetizeState.isAppetizeUrl,
    urlStatus,
    appetizeState.client,
    appetizeState.isInitializing,
    initializeAppetizeClient
  ]);

  // Prepare Sandpack files from HTML content (now expects inlined HTML)
  const prepareSandpackFiles = useCallback(() => {
    if (!htmlContent) return {};

    const files = {
      '/index.html': {
        code: htmlContent || '<div>No HTML content available</div>'
      }
    };

    return files;
  }, [htmlContent]);

  const container = getSelectedContainerObj();

  // Enhanced Figma panel with dropdown and zoom - Fixed to handle empty figmaData
  const renderFigmaOnlyPanel = () => {
    // Handle empty figmaData
    if (!figmaData || figmaData.length === 0) {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4 bg-gray-50">
          <FileX className="h-16 w-16 text-gray-400" />
          <div className="text-center">
            <p className="text-gray-600 text-lg font-medium mb-2">No Figma Designs</p>
            <p className="text-gray-500 text-sm max-w-md">
              No Figma designs have been added to this project yet. Add some designs to see them here.
            </p>
          </div>
        </div>
      );
    }

    // Handle case where figmaData exists but selectedScreen is null
    if (!selectedScreen) {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4 bg-gray-50">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-gray-600 text-sm">Loading Figma designs...</p>
        </div>
      );
    }

    return (
      <div className="w-full h-full flex flex-col">
        {/* Header with dropdown and controls */}
        <div className="flex items-center justify-between p-4 bg-gray-50 border-b border-gray-200">
          {/* Screen Selector Dropdown */}
          <div className="flex flex-col items-start gap-2" ref={dropdownRef}>
            {/* Dropdown */}
            <div className="relative w-full">
              <button
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors min-w-[200px]"
              >
                <Monitor className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700 truncate">
                  {selectedScreen.screen_name || selectedScreen.name}
                </span>
                <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
              </button>

              {isDropdownOpen && (
                <div className="absolute top-full left-0 mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                  {figmaData.map((screen) => (
                    <button
                      key={screen.screen_id || screen.id}
                      onClick={() => {
                        console.log('Screen selected from dropdown:', screen.screen_name || screen.name);
                        setSelectedScreen(screen);
                        setIsDropdownOpen(false);
                        setUseAllScreens(false);
                        resetZoom(); // Reset zoom when switching screens
                      }}
                      className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0 ${(selectedScreen.screen_id || selectedScreen.id) === (screen.screen_id || screen.id)
                        ? 'bg-blue-50 text-blue-600'
                        : 'text-gray-700'
                        }`}
                    >
                      <div className="flex items-center gap-2">
                        <Monitor className="h-4 w-4" />
                        <span className="text-sm font-medium">
                          {screen.screen_name || screen.name}
                        </span>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Zoom Controls */}
          <div className="flex items-center gap-2">
            <button
              onClick={zoomOut}
              disabled={zoomLevel <= 0.25}
              className="p-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Zoom Out"
            >
              <ZoomOut className="h-4 w-4 text-gray-600" />
            </button>

            <div className="px-3 py-1 bg-white border border-gray-300 rounded-lg min-w-[60px] text-center">
              <span className="text-sm font-medium text-gray-700">
                {Math.round(zoomLevel * 100)}%
              </span>
            </div>

            <button
              onClick={zoomIn}
              disabled={zoomLevel >= 3}
              className="p-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Zoom In"
            >
              <ZoomIn className="h-4 w-4 text-gray-600" />
            </button>

            <button
              onClick={resetZoom}
              className="p-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              title="Reset Zoom"
            >
              <RotateCcw className="h-4 w-4 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Image Display Area */}
        <div
          ref={imageContainerRef}
          className="flex-1 overflow-hidden bg-gray-100 relative"
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          style={{
            cursor: zoomLevel > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default'
          }}
        >
          <div
            className="w-full h-full flex items-center justify-center"
            style={{
              transform: `scale(${zoomLevel}) translate(${imagePosition.x / zoomLevel}px, ${imagePosition.y / zoomLevel}px)`,
              transition: isDragging ? 'none' : 'transform 0.1s ease-out'
            }}
          >
            <img
              src={selectedScreen.image_url}
              alt={selectedScreen.name}
              className="max-w-full max-h-full object-contain shadow-lg rounded-lg"
              style={{
                userSelect: 'none',
                pointerEvents: 'none'
              }}
              onError={(e) => {
                // Fallback to a placeholder if image fails to load
                e.target.src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMjUgNzVIMTc1VjEyNUgxMjVWNzVaIiBmaWxsPSIjOUI5RkFBIi8+CjxwYXRoIGQ9Ik0xMzUgOTVIMTY1VjEwNUgxMzVWOTVaIiBmaWxsPSIjNjM2OTc1Ii8+PC9zdmc+";
              }}
            />
          </div>
        </div>
      </div>
    );
  };

  // Render the main panel content (HTML preview or play button)
  const renderMainPanelContent = () => {
    // Show loading message when AI is typing
    if (isAiTyping) {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-gray-600 text-sm font-medium">Processing your request...</p>
          <p className="text-gray-500 text-xs text-center max-w-md">
            Please wait while we generate the code for your Figma design.
          </p>
        </div>
      );
    }

    // Show loading message while fetching HTML
    if (isLoadingHTML) {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-gray-600 text-sm font-medium">Loading preview...</p>
          <p className="text-gray-500 text-xs text-center max-w-md">
            Fetching HTML preview via WebSocket...
          </p>
        </div>
      );
    }

    // Show Sandpack if HTML content is available
    if (showSandpack && htmlContent) {
      const files = prepareSandpackFiles();

      return (
        <div className="w-full h-full">
          <div className="p-4 bg-gray-50 border-b border-gray-200 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Code className="h-4 w-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">
                HTML Preview - {selectedScreen?.screen_name || selectedScreen?.name}
              </span>
            </div>
            <button
              onClick={() => {
                console.log('Refresh button clicked');
                fetchScreenHTML(selectedScreen);
              }}
              className="p-1 hover:bg-gray-200 rounded transition-colors"
              title="Refresh preview"
            >
              <RefreshCw className="h-4 w-4 text-gray-600" />
            </button>
          </div>

          <div className="flex-1 overflow-y-auto">
            <Sandpack
              files={files}
              template="static"
              theme="light"
              options={{
                 showNavigator: false,
                showTabs: false,
                showLineNumbers: false,
                showInlineErrors: false,
                wrapContent: false,
                editorHeight: "90vh",
                editorWidthPercentage: 0,
                autoReload: true,
                recompileMode: "immediate",
                recompileDelay: 500,
                showConsole: false,
                showConsoleButton: false,
                layout: "preview" 
              }}
              customSetup={{
                entry: "/index.html"
              }}
            />
          </div>
        </div>
      );
    }

    // Show error message if there was an error fetching HTML
    if (htmlError) {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4">
          <AlertTriangle className="h-8 w-8 text-orange-500" />
          <p className="text-gray-600 text-sm font-medium">Failed to load preview</p>
          <p className="text-gray-500 text-xs text-center max-w-md">
            {htmlError}
          </p>
          <button
            onClick={() => {
              console.log('Retry button clicked');
              fetchScreenHTML(selectedScreen);
            }}
            className="px-4 py-2 bg-primary text-white rounded-md text-sm hover:bg-primary-600 transition-colors flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Retry
          </button>
        </div>
      );
    }

    // Show play button as fallback when no HTML content is available
    return (
      <div className="w-full h-full flex flex-col justify-center items-center space-y-4">
        <button
          onClick={handlePlayIconClick}
          className="w-16 h-16 bg-primary hover:bg-primary-600 rounded-full flex items-center justify-center text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
          title="Use these Figma screens to extract the design to code"
          disabled={!figmaData || figmaData?.length === 0 || !setUploadedAttachments || !setInputValue}
        >
          <Play className="w-8 h-8 ml-1" fill="currentColor" />
        </button>
        <p className="text-sm text-gray-700 text-center px-4">
          Click the play button to generate code from the selected design screen
        </p>
      </div>
    );

  };

  const renderFigmaPanel = () => (
    <div className="w-full h-full flex overflow-hidden">
      {/* Figma Panel - Left Side */}
      <div className="w-1/2 h-full overflow-hidden">
        {renderFigmaOnlyPanel()}
      </div>
      {/* Main Panel - Right Side */}
      <div className="w-1/2 h-full border-r border-gray-200 flex-shrink-0 overflow-hidden">
        {renderMainPanelContent()}
      </div>
    </div>
  );

  // Render main content panel
  const renderMainPanel = () => {
    // No containers available at all
    if (containers.length === 0) {
      return (
        <div className="w-full h-full flex items-center justify-center">
          <p className="text-gray-500 text-sm">No containers available</p>
        </div>
      );
    }

    // Container not found
    if (!selectedContainer || !container) {
      return (
        <div className="w-full h-full flex items-center justify-center">
          <p className="text-gray-500 text-sm">Loading container...</p>
        </div>
      );
    }

    // Container is running - show iframe or error
    if (container.status === 'running' && container.url) {
      // Show checking message while determining URL accessibility
      if (isCheckingUrl || urlStatus === 'checking') {
        return (
          <div className="w-full h-full flex items-center justify-center flex-col gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-gray-600 text-sm">
              Checking service availability...
            </p>
          </div>
        );
      }

      // Show error handling if URL is not accessible
      if (urlStatus === 'error' || iframeError) {
        const getErrorMessage = () => {
          switch (errorType) {
            case '404':
              return {
                icon: <AlertTriangle className="h-16 w-16 text-primary" />,
                title: "Project not available",
                description: "We couldn't find the project right now. It may be starting up — please check back shortly."
              };
            case '502':
              return {
                icon: <AlertTriangle className="h-16 w-16 text-primary" />,
                title: "Project is loading",
                description: "The project is taking longer than usual to respond. Please wait a moment and try again."
              };
            default:
              return {
                icon: <AlertTriangle className="h-16 w-16 text-primary" />,
                title: "Something went wrong",
                description: "We're having trouble connecting to the project. Please try again shortly."
              };
          }
        };

        const errorInfo = getErrorMessage();

        return (
          <div className="w-full h-full flex items-center justify-center flex-col gap-4">
            {errorInfo.icon}
            <p className="text-gray-600 text-sm font-medium text-center">
              {errorInfo.title}
            </p>
            <p className="text-gray-500 text-xs text-center max-w-md leading-relaxed">
              {errorInfo.description}
            </p>

            <button
              onClick={refreshIframe}
              className="px-6 py-2 bg-primary text-white rounded-md text-sm hover:bg-primary-600 transition-colors flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </button>
          </div>
        );
      }

      // URL is accessible - show iframe
      if (urlStatus === 'success') {
        const finalUrl = appetizeState.isAppetizeUrl
          ? ensureDebugUrl(processPreviewUrl(container.url))
          : processPreviewUrl(container.url);

        return (
          <div className="w-full h-full flex flex-col overflow-hidden">
            {/* Appetize Logs Panel */}
            {appetizeState.isAppetizeUrl && (
              <div className="flex-shrink-0 p-2">
                <AppetizeLogs
                  appetizeState={appetizeState}
                  onClearLogs={clearLogs}
                />
              </div>
            )}

            {/* Preview Iframe */}
            <div className="flex-1 relative overflow-hidden">
              <iframe
                ref={iframeRef}
                key={`${container.name}-${iframeKey}`}
                src={finalUrl}
                className="w-full h-full border-none overflow-hidden"
                title={`Preview - ${container.name}`}
                sandbox="allow-same-origin allow-scripts allow-forms allow-popups"
                allow="microphone; camera; midi; encrypted-media;"
                onError={() => {
                  // If iframe fails to load after URL check passed, show error
                  setIframeError(true);
                  setErrorType('other');
                }}
              />

              {/* Floating refresh button */}
              <button
                onClick={refreshIframe}
                className="absolute top-4 right-4 p-2 bg-black/10 hover:bg-black/20 rounded-md transition-colors backdrop-blur-sm"
                title="Refresh preview"
              >
                <RefreshCw className="h-4 w-4 text-gray-700" />
              </button>
            </div>
          </div>
        );
      }
    }

    // Container is building/starting
    if (container.status === 'building' || container.status === 'starting') {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-gray-600 text-sm">
            {container.name} is {container.status}...
          </p>
          <p className="text-gray-500 text-xs text-center max-w-md">
            Please wait while the container starts up. This may take a few moments.
          </p>
        </div>
      );
    }

    // Container is stopped/not started/failed
    return (
      <div className="w-full h-full flex items-center justify-center flex-col gap-4">
        {container.status === 'failed' || container.status === 'error' ? (
          <>
            <AlertCircle className="h-8 w-8 text-red-500" />
            <p className="text-gray-600 text-sm">
              {container.name} failed to start
            </p>
            {container.error && (
              <p className="text-gray-500 text-xs max-w-md text-center">
                {container.error}
              </p>
            )}
          </>
        ) : container.status === ContainerStatusTypes.NOT_STARTED ? (
          <>
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-gray-500 text-xs text-center max-w-md">
              The container is being automatically started. Please wait a moment.
            </p>
          </>
        ) : (
          <>
            <Square className="h-8 w-8 text-gray-400" />
            <p className="text-gray-600 text-sm">
              {container.name} is {container.status || 'not started'}
            </p>
          </>
        )}
      </div>
    );
  };

  // Main render based on showMode
  if (showMode === "figma") {
    return renderFigmaPanel();
  }

  if (showMode === "main") {
    return (
      <div className="w-full h-full">
        {renderMainPanel()}
      </div>
    );
  }

  // Default split screen layout (backward compatibility)
  return (
    <div className="w-full h-full flex overflow-hidden">
      {renderMainPanel()}
    </div>
  );
};

export default FigmaPreviewPanel;