import { getHeaders } from "./api";

const base_url = process.env.NEXT_PUBLIC_API_URL;
const SHOW_NAME = 'manifest';

/**
 * Get manifest form configuration for React JSON Schema Form
 * @param {number} projectId - Project ID to get form config for
 * @returns {Promise<Object>} Response containing jsonSchema, uiSchema, and formData
 */
export const getManifestFormConfig = async (projectId) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/form-config/${projectId}/`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to get manifest form configuration');
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * Save manifest data from form submission
 * @param {number} projectId - Project ID to save manifest for
 * @param {Object} formData - Form data from RJSF form
 * @returns {Promise<Object>} Response from the server
 */
export const saveManifestFormData = async (projectId, formData) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/form-data/${projectId}/`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify(formData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to save manifest data');
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * Validate manifest schema generation (for testing/debugging)
 * @returns {Promise<Object>} Response with schema validation info
 */
export const validateManifestSchema = async () => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/validate-schema/`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to validate manifest schema');
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};


/**
 * Generate or retrieve project manifest with RJSF form configuration
 * @param {number} projectId - Project ID to generate manifest for
 * @param {boolean} regenerate - Force regenerate even if manifest exists (default: false)
 * @param {boolean} includeFormConfig - Include JSON Schema and UI Schema for RJSF (default: true)
 * @returns {Promise<Object>} Response containing:
 *   - success: boolean
 *   - formData: Generated manifest in RJSF format
 *   - jsonSchema: JSON Schema for the form (if includeFormConfig is true)
 *   - uiSchema: UI Schema for the form (if includeFormConfig is true)
 *   - manifest: Raw manifest object
 *   - manifest_yaml: YAML string version
 *   - source: 'existing' | 'generated' | 'regenerated'
 *   - generated_at: ISO timestamp
 *   - generation_time_ms: Generation time in milliseconds (if generated)
 *   - build_ids: Array of build IDs used (if generated)
 */
export const generateManifestJson = async (projectId, regenerate = false, includeFormConfig = true) => {
  try {
    const queryParams = new URLSearchParams({
      regenerate: regenerate.toString(),
      include_form_config: includeFormConfig.toString()
    });

    const response = await fetch(
      `${base_url}/${SHOW_NAME}/generate_manifest_json/${projectId}/?${queryParams}`, 
      {
        method: 'POST',
        headers: await getHeaders()
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || errorData.detail || 'Failed to generate manifest');
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * Update project manifest with selective field updates
 * Only modified fields are updated, preserving existing data
 * @param {number} projectId - Project ID to update manifest for
 * @param {string} manifestYaml - Project manifest content in YAML format
 * @returns {Promise<Object>} Response containing:
 *   - message: Success/status message
 *   - project_id: Project ID
 *   - updated_fields: Array of field names that were updated
 */
export const updateProjectManifest = async (projectId, manifestYaml) => {
  try {
    const response = await fetch(
      `${base_url}/${SHOW_NAME}/update_project_manifest/${projectId}/`, 
      {
        method: 'PUT',
        headers: await getHeaders(),
        body: JSON.stringify({
          project_manifest: manifestYaml
        })
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || errorData.detail || 'Failed to update project manifest');
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};